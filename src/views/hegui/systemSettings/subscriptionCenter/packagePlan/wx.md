---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 10-订单管理服务/统一支付接口

## POST 微信支付回调处理

POST /whiskerguardorderservice/union/pay/wechat/notify

{@code POST  /union/pay/wechat/notify} : Handle WeChat Pay callback.

> Body 请求参数

```json
"string"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Wechatpay-Serial|header|string| 是 |微信支付序列号|
|Wechatpay-Signature|header|string| 是 |微信支付签名|
|Wechatpay-Timestamp|header|string| 是 |微信支付时间戳|
|Wechatpay-Nonce|header|string| 是 |微信支付随机数|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|string| 否 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

# 数据模型
