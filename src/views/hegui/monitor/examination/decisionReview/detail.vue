<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Document,
  Edit,
  Promotion,
  VideoPlay,
} from '@element-plus/icons-vue'
import decisionApi from '@/api/review/decision'
import dictApi from '@/api/modules/system/dict'
import DocumentUpload from '@/components/DocumentUpload/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import InitiateDialog from '@/components/initiate/index.vue'
import OtherResultDialog from '@/components/OtherResultDialog/index.vue'

const route = useRoute()
const router = useRouter()

const activeTab = ref('detail')
const loading = ref(false)
const decisionDetail = ref<any>({})
const decisionTypeOptions = ref<any[]>([])
const accordingTypeOptions = ref<any[]>([])

// 审查发起弹窗相关数据
const showInitiateDialog = ref(false)

// 审查结果弹窗相关数据
const showOtherResultDialog = ref(false)

// 审查记录弹窗相关数据
const showRecordsDialog = ref(false)
const reviewRecords = ref<any[]>([])
const recordsLoading = ref(false)

// 获取决策类型选项
async function getDecisionTypeOptions() {
  try {
    const response = await dictApi.dictAll(57)
    if (response) {
      decisionTypeOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取决策类型选项失败:', error)
  }
}

// 获取依据类型选项
async function getAccordingTypeOptions() {
  try {
    const response = await dictApi.dictAll(55)
    if (response) {
      accordingTypeOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取依据类型选项失败:', error)
  }
}

// 根据字典值获取标签
function getDictLabel(value: string, options: any[]) {
  if (!value || !options || !Array.isArray(options)) {
    return value || '-'
  }
  const option = options.find(item => item && item.value === value)
  return option ? option.label : value
}

// 获取决策级别标签
function getLevelLabel(level: string) {
  switch (level) {
    case 'GENERAL': return '一般'
    case 'IMPORTANT': return '重要'
    case 'CRITICAL': return '关键'
    default: return level
  }
}

// 获取状态标签
function getStatusLabel(status: string) {
  switch (status) {
    case 'DRAFT': return '草稿'
    case 'PENDING': return '待审查'
    case 'PUBLISHED': return '发布'
    case 'REVIEWING': return '审核中'
    case 'REVOKE': return '已撤回'
    default: return status
  }
}

// 获取状态标签类型
function getStatusTagType(status: string) {
  switch (status) {
    case 'DRAFT': return 'info'
    case 'PENDING': return 'warning'
    case 'PUBLISHED': return 'success'
    case 'REVIEWING': return 'primary'
    case 'REVOKE': return 'danger'
    default: return 'info'
  }
}

// 获取相关方类型标签
function getRelatedTypeLabel(type: string) {
  switch (type) {
    case 'SUPPLIER': return '供应商'
    case 'CUSTOMER': return '客户'
    case 'PARTNER': return '合作伙伴'
    case 'REGULATOR': return '监管机构'
    default: return type || '类型未知'
  }
}

// 获取详情数据
async function getDecisionDetail() {
  const id = route.query.id as string
  if (!id) {
    ElMessage.error('缺少审查ID参数')
    return
  }

  try {
    loading.value = true
    const response = await decisionApi.decisionReview(null, { id }, 'info')
    if (response) {
      decisionDetail.value = response
    }
    else {
      ElMessage.error(response.message || '获取详情失败')
    }
  }
  catch (error) {
    // console.error('获取详情失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }
  finally {
    loading.value = false
  }
}
function editItem() {
  router.push({
    path: '/monitor/examination/decisionReview/addEdit',
    query: { id: route.query.id },
  })
}
// 返回列表页
function goBack() {
  // router.push('/monitor/examination/decisionReview')
  router.back()
}

// 检查审核权限
async function checkAuditPermission() {
  if (!decisionDetail.value.id) {
    return false
  }

  try {
    const res = await decisionApi.getComplianceProcess({
      objectId: decisionDetail.value.id,
      reviewType: 'DECISION',
    })
    // 根据接口返回的isAudit字段判断审核权限
    return res
  }
  catch (err) {
    // console.error('检查审核权限失败:', err)
    return false
  }
}

// 提交审查
async function submitReview() {
  if (!decisionDetail.value.id) {
    ElMessage.error('参数错误')
    return
  }

  // 根据complianceReview字段判断跳转页面
  if (decisionDetail.value.complianceReview?.id === null) {
    // complianceReview为null，显示审查发起弹窗
    showInitiateDialog.value = true
  }
  else {
    // complianceReview不为null，先检查审核权限
    const hasAuditPermission = await checkAuditPermission()
    if (!hasAuditPermission) {
      ElMessage.error('暂无审核权限')
      return
    }

    if (hasAuditPermission.isSubmit) {
      // 显示审查发起弹窗
      showInitiateDialog.value = true
      return
    }
    else {
      if (!hasAuditPermission.isAudit) {
        ElMessage.error('暂无审核权限')
        return
      }
    }

    // 显示审查结果弹窗
    ElMessageBox.confirm(
      '确定要进行合规审查吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    ).then(() => {
      showOtherResultDialog.value = true
    }).catch(() => {
      // 用户点击了取消
      // 不进行智能审查操作
    })
  }
}

// 处理审查发起弹窗成功事件
function handleInitiateSuccess() {
  // 刷新页面数据
  getDecisionDetail()
}

// 审查记录
function reviewRecord() {
  if (!decisionDetail.value.id) {
    ElMessage.error('缺少审查ID参数')
    return
  }
  showRecordsDialog.value = true
  getReviewRecords()
}

// 获取审查记录
async function getReviewRecords() {
  try {
    recordsLoading.value = true
    const response = await decisionApi.queryAllOtherReviews({
      page: 0,
      size: 20,
      reviewId: decisionDetail.value.id,
      reviewType: 'DECISION',
    })
    reviewRecords.value = response
  }
  catch (error) {
    console.error('获取审查记录失败:', error)
    ElMessage.error('获取审查记录失败')
  }
  finally {
    recordsLoading.value = false
  }
}

// 获取审查状态标签类型
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    REJECTED: 'danger',
  }
  return statusMap[status] || 'info'
}

// 获取审查状态文本
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: '待审查',
    IN_PROGRESS: '审查中',
    COMPLETED: '已完成',
    REJECTED: '已拒绝',
  }
  return statusMap[status] || status
}

// 关闭审查记录弹窗
function handleRecordsDialogClose() {
  showRecordsDialog.value = false
  reviewRecords.value = []
}

// 页面加载时获取详情
onMounted(() => {
  getDecisionTypeOptions()
  getAccordingTypeOptions()
  getDecisionDetail()
})
</script>

<template>
  <div class="absolute-container">
    <div style="height: 100%;overflow-y: auto;">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex">
            <div class="mt-4 flex items-center justify-between">
              <h1 class="text-xl c-[#000000] font-bold">
                {{ decisionDetail.name || '重大决策审查详情' }}
              </h1>
              <el-tag :type="getStatusTagType(decisionDetail.status || '')" class="ml-4">
                {{ getStatusLabel(decisionDetail.status) }}
              </el-tag>
            </div>
            <div class="flex items-center space-x-3">
              <!-- <el-button v-auth="'decisionReview/detail/startReview'" type="primary" class="!rounded-button" @click="startReview">
                <el-icon class="mr-2">
                  <VideoPlay />
                </el-icon>开始审查
              </el-button> -->
              <el-button v-auth="'decisionReview/detail/submitReview'" :disabled="decisionDetail.status === 'PUBLISHED'" plain class="!rounded-button" @click="submitReview" v-debounce="2000">
                <el-icon class="mr-2">
                  <Promotion />
                </el-icon>一键审核
              </el-button>
              <el-button v-auth="'decisionReview/detail/reviewRecord'" plain class="!rounded-button" @click="reviewRecord" v-debounce="2000">
                <el-icon class="mr-2">
                  <Document />
                </el-icon>审查记录
              </el-button>
              <!-- 编辑 v-auth="'decisionReview/detail/edit'" -->
              <el-button v-auth="'decisionReview/detail/edit'" plain class="!rounded-button" @click="editItem" v-debounce="2000">
                <el-icon class="mr-2">
                  <Edit />
                </el-icon>编辑
              </el-button>
              <el-button v-auth="'decisionReview/detail/back'" plain class="!rounded-button" @click="goBack" v-debounce="2000">
                <el-icon class="mr-2">
                  <ArrowLeft />
                </el-icon>返回
              </el-button>
            </div>
          </div>
        </template>
      </page-header>
      <PageMain style="background-color: transparent;">
        <el-card class="">
          <div class="grid grid-cols-2 mb-6 gap-6">
            <div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">审查编号</label>
                <p class="font-medium">
                  {{ decisionDetail.reviewCode || '-' }}
                </p>
              </div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">决策名称</label>
                <p class="font-medium">
                  {{ decisionDetail.name || '-' }}
                </p>
              </div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">决策类型</label>
                <p class="font-medium">
                  {{ getDictLabel(decisionDetail?.decisionType, decisionTypeOptions) || '-' }}
                </p>
              </div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">决策级别</label>
                <p class="font-medium">
                  <el-tag :type="decisionDetail.level === 'CRITICAL' ? 'danger' : decisionDetail.level === 'IMPORTANT' ? 'warning' : 'info'">
                    {{ getLevelLabel(decisionDetail.level) || '-' }}
                  </el-tag>
                </p>
              </div>
              <div>
                <label class="block text-sm text-gray-500">发起部门</label>
                <p class="font-medium">
                  {{ decisionDetail.departmentName || '-' }}
                </p>
              </div>
            </div>
            <div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">审查人员</label>
                <p class="font-medium">
                  {{ decisionDetail.auditNameBy || '-' }}
                </p>
              </div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">创建时间</label>
                <p class="font-medium">
                  {{ decisionDetail.createdAt || '-' }}
                </p>
              </div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">审查截止日期</label>
                <p class="font-medium">
                  {{ decisionDetail.deadlineDate || '-' }}
                </p>
              </div>
              <div class="mb-4">
                <label class="block text-sm text-gray-500">审查状态</label>
                <p class="font-medium">
                  <el-tag :type="getStatusTagType(decisionDetail.status || '')">
                    {{ getStatusLabel(decisionDetail.status) || '-' }}
                  </el-tag>
                </p>
              </div>
              <div>
                <label class="block text-sm text-gray-500">审查说明</label>
                <p class="font-medium">
                  {{ decisionDetail.explain || '-' }}
                </p>
              </div>
            </div>
          </div>
        </el-card>
        <el-card class="mt-20">
          <el-tabs v-model="activeTab">
            <h3 class="mb-4 text-base font-bold">
              决策背景
            </h3>
            <p class="mb-6 text-sm text-gray-700">
              {{ decisionDetail.background || '暂无决策背景信息' }}
            </p>

            <h3 class="mb-4 text-base font-bold">
              决策目的
            </h3>
            <p class="mb-6 text-sm text-gray-700">
              {{ decisionDetail.purpose || '暂无决策目的信息' }}
            </p>

            <h3 class="mb-4 text-base font-bold">
              决策方案
            </h3>
            <p class="mb-6 text-sm text-gray-700">
              {{ decisionDetail.proposal || '暂无决策方案信息' }}
            </p>

            <h3 class="mb-4 text-base font-bold">
              实施计划
            </h3>
            <p class="mb-6 text-sm text-gray-700">
              {{ decisionDetail.plan || '暂无实施计划信息' }}
            </p>

            <h3 class="mb-4 text-base font-bold">
              预期结果
            </h3>
            <p class="mb-6 text-sm text-gray-700">
              {{ decisionDetail.expectedResult || '暂无预期结果信息' }}
            </p>

            <div v-if="decisionDetail.money" class="mb-6">
              <h3 class="mb-4 text-base font-bold">
                涉及金额
              </h3>
              <p class="text-sm text-gray-700">
                {{ decisionDetail.money ? `¥${decisionDetail.money.toLocaleString()}` : '-' }}
              </p>
            </div>

            <h3 class="mb-4 text-base font-bold">
              决策依据
            </h3>
            <el-table v-if="decisionDetail.decisionAccordingList && decisionDetail.decisionAccordingList.length > 0" :data="decisionDetail.decisionAccordingList" class="mb-6">
              <el-table-column prop="name" label="依据名称" />
              <el-table-column prop="accordingType" label="依据类型">
                <template #default="{ row }">
                  {{ getDictLabel(row?.accordingType, accordingTypeOptions) }}
                </template>
              </el-table-column>
              <el-table-column prop="explain" label="说明" />
              <el-table-column prop="describe" label="描述" />
            </el-table>
            <p v-else class="mb-6 text-sm text-gray-500">
              暂无决策依据信息
            </p>

            <h3 class="mb-4 text-base font-bold">
              决策文档
            </h3>
            <div v-if="decisionDetail.documentUrl" class="mb-6">
              <DocumentUpload
                :model-value="decisionDetail.documentUrl"
                :readonly="true"
                category-name="document"
                :tip-text="decisionDetail.document ? `文档名称: ${decisionDetail.document}` : '决策文档'"
              />
            </div>
            <div v-else class="mb-6 text-sm text-gray-500">
              暂无决策文档
            </div>

            <h3 class="mb-4 text-base font-bold">
              相关政策文件
            </h3>
            <div v-if="decisionDetail.decisionPolicyList && decisionDetail.decisionPolicyList.length > 0" class="mb-6">
              <UploadMbb
                :model-value="decisionDetail.decisionPolicyList"
                :readonly="true"
                category-name="policy"
                tip-text="决策相关政策文档"
              />
            </div>
            <div v-else class="mb-6 text-sm text-gray-500">
              暂无相关政策文件
            </div>

            <h3 class="mb-4 text-base font-bold">
              决策附件
            </h3>
            <div v-if="decisionDetail.decisionAttachmentList && decisionDetail.decisionAttachmentList.length > 0" class="mb-6">
              <UploadMbb
                :model-value="decisionDetail.decisionAttachmentList"
                :readonly="true"
                category-name="attachment"
                tip-text="决策相关附件文档"
              />
            </div>
            <p v-else class="mb-6 text-sm text-gray-500">
              暂无决策附件
            </p>

            <h3 class="mb-4 text-base font-bold">
              决策相关方
            </h3>
            <div v-if="decisionDetail.decisionRelatedPartyList && decisionDetail.decisionRelatedPartyList.length > 0" class="mb-6">
              <el-row :gutter="16">
                <el-col v-for="(party, index) in decisionDetail.decisionRelatedPartyList" :key="index" :span="8">
                  <el-card class="mb-4">
                    <div class="mb-2 text-sm text-gray-900 font-medium">
                      {{ party.relatedName || party.name || '未命名相关方' }}
                    </div>
                    <div class="mb-1 text-xs text-gray-500">
                      类型: {{ getRelatedTypeLabel(party.relatedType || party.type) }}
                    </div>
                    <div v-if="party.contact" class="mb-1 text-xs text-gray-600">
                      联系方式: {{ party.contact }}
                    </div>
                    <div v-if="party.role" class="mb-1 text-xs text-blue-600">
                      角色: {{ party.role }}
                    </div>
                    <div v-if="party.relatedExplain" class="text-xs text-gray-600">
                      关系说明: {{ party.relatedExplain }}
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
            <p v-else class="mb-6 text-sm text-gray-500">
              暂无相关方信息
            </p>

            <h3 class="mb-4 text-base font-bold">
              内外部沟通情况
            </h3>
            <div class="mb-6">
              <div v-if="decisionDetail.innerCommunication" class="mb-4">
                <h4 class="mb-2 text-sm text-gray-800 font-medium">
                  内部沟通情况
                </h4>
                <p class="text-sm text-gray-700">
                  {{ decisionDetail.innerCommunication }}
                </p>
              </div>
              <div v-if="decisionDetail.outerCommunication" class="mb-4">
                <h4 class="mb-2 text-sm text-gray-800 font-medium">
                  外部沟通情况
                </h4>
                <p class="text-sm text-gray-700">
                  {{ decisionDetail.outerCommunication }}
                </p>
              </div>
              <p v-if="!decisionDetail.innerCommunication && !decisionDetail.outerCommunication" class="text-sm text-gray-500">
                暂无沟通情况信息
              </p>
            </div>

            <h3 class="mb-4 text-base font-bold">
              风险评估
            </h3>
            <div class="mb-6">
              <div v-if="decisionDetail.riskSelf" class="mb-4">
                <h4 class="mb-2 text-sm text-gray-800 font-medium">
                  风险自评
                </h4>
                <p class="text-sm text-gray-700">
                  {{ decisionDetail.riskSelf }}
                </p>
              </div>
              <div v-if="decisionDetail.riskAssess" class="mb-4">
                <h4 class="mb-2 text-sm text-gray-800 font-medium">
                  风险评估
                </h4>
                <p class="text-sm text-gray-700">
                  {{ decisionDetail.riskAssess }}
                </p>
              </div>
              <div v-if="decisionDetail.legalImpact" class="mb-4">
                <h4 class="mb-2 text-sm text-gray-800 font-medium">
                  法律影响
                </h4>
                <p class="text-sm text-gray-700">
                  {{ decisionDetail.legalImpact }}
                  <span v-if="decisionDetail.legalImpactDegree" class="ml-2">
                    <el-tag :type="decisionDetail.legalImpactDegree === 'HIGH' ? 'danger' : decisionDetail.legalImpactDegree === 'MEDIUM' ? 'warning' : 'info'" size="small">
                      {{ decisionDetail.legalImpactDegree === 'HIGH' ? '高风险' : decisionDetail.legalImpactDegree === 'MEDIUM' ? '中风险' : '低风险' }}
                    </el-tag>
                  </span>
                </p>
              </div>
              <div v-if="decisionDetail.financialImpact" class="mb-4">
                <h4 class="mb-2 text-sm text-gray-800 font-medium">
                  财务影响
                </h4>
                <p class="text-sm text-gray-700">
                  {{ decisionDetail.financialImpact }}
                  <span v-if="decisionDetail.financialImpactDegree" class="ml-2">
                    <el-tag :type="decisionDetail.financialImpactDegree === 'HIGH' ? 'danger' : decisionDetail.financialImpactDegree === 'MEDIUM' ? 'warning' : 'info'" size="small">
                      {{ decisionDetail.financialImpactDegree === 'HIGH' ? '高风险' : decisionDetail.financialImpactDegree === 'MEDIUM' ? '中风险' : '低风险' }}
                    </el-tag>
                  </span>
                </p>
              </div>
              <p v-if="!decisionDetail.riskSelf && !decisionDetail.riskAssess && !decisionDetail.legalImpact && !decisionDetail.financialImpact" class="text-sm text-gray-500">
                暂无风险评估信息
              </p>
            </div>
            <!-- <el-tab-pane label="审查记录" name="record" />
            <el-tab-pane label="法规匹配" name="law" />
            <el-tab-pane label="风险识别" name="risk" />
            <el-tab-pane label="审查意见" name="opinion" />
            <el-tab-pane label="操作历史" name="history" /> -->
          </el-tabs>
        </el-card>
      </PageMain>
    </div>

    <!-- 审查发起弹窗 -->
    <InitiateDialog
      v-model:visible="showInitiateDialog"
      :object-id="decisionDetail.id"
      review-type="DECISION"
      :compliance-review="decisionDetail.complianceReview"
      @success="handleInitiateSuccess"
    />

    <!-- 审查结果弹窗 -->
    <OtherResultDialog
      v-model:visible="showOtherResultDialog"
      :object-id="decisionDetail.id"
      review-type="DECISION"
      :compliance-review="decisionDetail.complianceReview"
      @success="handleInitiateSuccess"
    />

    <!-- 审查记录弹窗 -->
    <el-dialog
      v-model="showRecordsDialog"
      title="审查记录"
      width="800px"
      :close-on-click-modal="false"
      @close="handleRecordsDialogClose"
    >
      <div v-loading="recordsLoading" class="records-content">
        <el-table :data="reviewRecords" style="width: 100%" max-height="500px" empty-text="暂无审查记录">
          <el-table-column prop="comment" label="审查意见" width="120" />
          <el-table-column prop="taskName" label="任务名称" width="100" />
          <el-table-column prop="createdBy" label="创建人" width="160" />
          <el-table-column prop="createdAt" label="创建时间" width="160" />
          <el-table-column label="审查记录" min-width="200">
            <template #default="{ row }">
              <el-tooltip
                placement="top"
                :content="row.contentList?.map(item => item.content).join('\n')"
                :show-after="200"
              >
                <div class="content-preview">
                  {{ row.contentList?.map(item => item.content).join('\n').substring(0, 50) + (row.contentList?.map(item => item.content).join('\n').length > 50 ? '...' : '') }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleRecordsDialogClose">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
  [type="number"]::-webkit-inner-spin-button,
  [type="number"]::-webkit-outer-spin-button {
    margin: 0;
    appearance: none;
  }

  :deep(.el-card) {
    border-radius: 4px;
  }

  :deep(.el-tabs__item) {
    height: 48px;
    padding: 0 16px;
    line-height: 48px;
  }

  :deep(.el-table) {
    font-size: 14px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f9fafb;
  }

  :deep(.el-table .cell) {
    white-space: pre-line;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }

  .records-content {
    min-height: 300px;
  }

  .dialog-footer {
    text-align: right;
  }

  .content-preview {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    cursor: pointer;
    padding: 4px 0;
  }
</style>
