<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const route = useRoute()
const router = useRouter()

const tabsList = ref([
  {
    id: 1,
    name: '基本信息',
  },
  {
    id: 2,
    name: '关联信息',
  },
  {
    id: 3,
    name: '风险分析',
  },
])

const activeName = ref(1)
const loading = ref(false)
const detailData = ref({})

// 获取详情数据
async function getDetail() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少义务ID')
    return
  }

  try {
    loading.value = true
    const response = await systemApi.complianceSystem({ id }, 'info')
    if (response) {
      detailData.value = response
    }
  }
  catch (error) {
    console.error('获取义务详情失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }
  finally {
    loading.value = false
  }
}

// 编辑义务
function goAddEdit() {
  router.push({
    path: '/database/duty/addEdit',
    query: {
      id: route.query.id,
    },
  })
}

// 删除
async function deleteObligation() {
  try {
    await ElMessageBox.confirm(
      `确定要删除「${detailData.value.title}」吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.complianceSystem({ id: route.query.id }, 'delete')
    ElMessage.success('删除成功')
    router.push('/database/duty')
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取风险等级显示
function getRiskLevelInfo(level: string) {
  const levelMap = {
    GENERAL: { text: '一般风险', type: 'info' },
    MAJOR: { text: '重大风险', type: 'danger' },
    TYPICAL: { text: '典型风险', type: 'warning' },
    SAFE: { text: '安全', type: 'success' },
  }
  return levelMap[level] || { text: '未知', type: 'info' }
}

// 获取状态显示
function getStatusInfo(status: string) {
  const statusMap = {
    DRAFT: { text: '草稿', type: 'info' },
    EFFECTIVE: { text: '生效中', type: 'success' },
    EXPIRED: { text: '已失效', type: 'danger' },
    REVIEWING: { text: '待审核', type: 'warning' },
  }
  return statusMap[status] || { text: '未知', type: 'info' }
}

// 获取义务类型显示
function getObligationTypeText(type: string) {
  const typeMap = {
    LAR: '法律法规',
    SUPERVISE: '监管要求',
    MS: '强制标准',
    ES: '企业标准',
  }
  return typeMap[type] || '未知'
}

// 获取来源类型显示
function getSourceTypeText(type: string) {
  const typeMap = {
    LAWS: '法规',
  }
  return typeMap[type] || '未知'
}

onMounted(() => {
  getDetail()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="aic flex">
            <div class="f-20 mr-16 fw-600">
              {{ detailData.title || '义务详情' }}
            </div>
            <el-tag :type="getStatusInfo(detailData.status).type">
              {{ getStatusInfo(detailData.status).text }}
            </el-tag>
          </div>
          <div>
            <el-button type="primary" @click="goAddEdit()">
              <svg-icon name="ep:edit" />
              <span class="ml-8">编辑</span>
            </el-button>
            <el-button>
              <svg-icon name="ep:share" />
              <span class="ml-8">导出</span>
            </el-button>
            <el-button type="danger" @click="deleteObligation()">
              <svg-icon name="ep:delete" />
              <span class="ml-8">删除</span>
            </el-button>
            <el-button>
              <svg-icon name="ep:paperclip" />
              <span class="ml-8">关联制度</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div class="card f-14" style="min-height: 118px;padding: 24px;">
        <el-row v-loading="loading">
          <el-col :span="8">
            <div>
              <div>
                <span class="c-[#666666]">义务编号：</span>
                <span class="c-[#000000] fw-500">{{ detailData.obligationCode || '未设置' }}</span>
              </div>
              <div class="mt-18">
                <span class="c-[#666666]">风险等级：</span>
                <el-tag :type="getRiskLevelInfo(detailData.level).type">
                  {{ getRiskLevelInfo(detailData.level).text }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div>
              <div>
                <span class="c-[#666666]">义务类型：</span>
                <span class="c-[#000000] fw-500">{{ getObligationTypeText(detailData.obligationType) }}</span>
              </div>
              <div class="mt-18">
                <span class="c-[#666666]">生效日期：</span>
                <span class="c-[#000000] fw-500">{{ detailData.effectiveDate || '未设置' }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div>
              <div>
                <span class="c-[#666666]">来源类型：</span>
                <span class="c-[#000000] fw-500">{{ getSourceTypeText(detailData.sourceType) }}</span>
              </div>
              <div class="mt-18">
                <span class="c-[#666666]">责任部门：</span>
                <span class="c-[#000000] fw-500">{{ detailData.department || '未指定' }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="card mt-24 p-24">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i, j in tabsList" :key="j" :label="i.name" :name="i.id">
            <div v-if="activeName == 1">
              <div class="f-16 fw-500">
                义务描述
              </div>
              <div class="f-14 p-16 fw-400">
                {{ detailData.content }}
              </div>
              <div class="f-16 fw-500">
                核心要点
              </div>
              <div>
                <div class="f-14 p-16 fw-500">
                  {{ detailData.corePoints }}
                </div>
                <div class="f-16 fw-500">
                  适用条件
                </div>
                <div class="f-14 p-16 fw-400">
                  {{ detailData.applicability }}
                  <!-- 金融机构应当按照规定建立客户身份识别制度、客户身份资料和交易记录保存制度、大额交易和可疑交易报告制度，履行反洗钱义务。 -->
                </div>
              </div>
              <!-- <div class="f-16 mt-24 fw-500">
                执行要求
              </div> -->
              <!-- <div>
                <div v-for="i, j in 4" :key="j" class="aic mt-12 flex">
                  <div>
                    <svg-icon name="ep:circle-check" style="color: #22c55e;" />
                  </div>
                  <div class="f-14 ml-8">
                    建立健全的客户身份识别制度
                  </div>
                </div>
              </div> -->
            </div>
            <div v-if="activeName == 2" />
            <div v-if="activeName == 3" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
