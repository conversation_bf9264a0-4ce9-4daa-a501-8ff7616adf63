<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import systemApi from '@/api/complianceApi/one/systemManagement'

// 定义props
interface Props {
  searchParams?: Record<string, any>
  refreshTrigger?: number
  isIntelligentMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({}),
  refreshTrigger: 0,
  isIntelligentMode: false,
})

// 定义emits
const emit = defineEmits<{
  'update:loading': [loading: boolean]
  'update:total': [total: number]
  'dataLoaded': [data: any[]]
}>()

const router = useRouter()

// 表格数据
const tableData = ref([])

// 分页数据
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 加载状态
const loading = ref(false)

// 请求控制器，用于取消之前的请求
let currentAbortController: AbortController | null = null

// 多选功能
const regulationIds = ref<any[]>([])
const multipleSelection = ref<any[]>([])

// 处理多选变化
function handleSelectionChange(selection: any[]) {
  multipleSelection.value = selection
  regulationIds.value = selection.map(item => item.id)
}

// 获取列表数据
async function getList() {
  try {
    // 取消之前的请求
    if (currentAbortController) {
      currentAbortController.abort()
    }

    // 创建新的请求控制器
    currentAbortController = new AbortController()
    const signal = currentAbortController.signal

    loading.value = true
    emit('update:loading', true)

    let response

    // 记录当前请求的模式，用于验证响应
    const currentMode = props.isIntelligentMode

    if (props.isIntelligentMode) {
      // 智能推荐模式：调用智能推荐接口，不需要分页参数
      response = await systemApi.intelligentRecommendations()
    }
    else {
      // 普通模式：调用原有接口，需要分页参数
      const params = {
        ...props.searchParams,
        page: pagination.value.page,
        limit: pagination.value.limit,
      }
      response = await systemApi.lawsSystem(params, 'list')
    }

    // 检查请求是否被取消
    if (signal.aborted) {
      return
    }

    // 验证响应是否对应当前模式（双重保险）
    if (currentMode !== props.isIntelligentMode) {
      return
    }

    if (response) {
      if (props.isIntelligentMode) {
        // 智能推荐接口直接返回数组
        tableData.value = response || []
        pagination.value.total = response?.length || 0
        emit('update:total', pagination.value.total)
        emit('dataLoaded', response || [])
      }
      else {
        // 普通接口返回分页数据
        tableData.value = response.content || []
        pagination.value.total = response.totalElements || 0
        emit('update:total', pagination.value.total)
        emit('dataLoaded', response.content || [])
      }
    }
  }
  catch (error: any) {
    // 忽略被取消的请求错误
    if (error.name === 'AbortError') {
      return
    }

    // 错误提示已在响应拦截器中统一处理
  }
  finally {
    loading.value = false
    emit('update:loading', false)
    // 清理当前控制器
    if (currentAbortController && !currentAbortController.signal.aborted) {
      currentAbortController = null
    }
  }
}

// 查看详情
function viewDetail(row: any) {
  router.push({
    path: '/database/laws/detail',
    query: { id: row.id },
  })
}

// 编辑
function editRegulation(row: any) {
  router.push({
    path: '/database/laws/addEdit',
    query: { id: row.id, mode: 'edit' },
  })
}

// 订阅状态管理
const subscribingIds = ref(new Set<number>())

// 订阅
async function subscribe(row: any) {
  // 防止重复点击
  if (subscribingIds.value.has(row.id)) {
    return
  }

  try {
    subscribingIds.value.add(row.id)

    // 检查是否已订阅
    if (row.isRelated) {
      // 已订阅，执行取消订阅
      await unsubscribe(row)
      return
    }

    // 执行订阅
    const params = {
      regulationId: row.id,
    }

    await systemApi.isSubscribe(params)
    ElMessage.success(`已订阅：${row.title}`)

    // 更新本地状态
    row.isRelated = true

    // 可选：刷新列表以获取最新状态
    // getList()
  }
  catch (error) {
    ElMessage.error('订阅失败，请稍后重试')
  }
  finally {
    subscribingIds.value.delete(row.id)
  }
}

// 取消订阅
async function unsubscribe(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要取消订阅「${row.title}」吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.deleteSubscribe(row.id)
    ElMessage.success(`已取消订阅：${row.title}`)

    // 更新本地状态
    row.isRelated = false

    // 可选：刷新列表以获取最新状态
    // getList()
  }
  catch (error) {
    if (error !== 'cancel') {
      // 错误提示已在响应拦截器中统一处理
    }
  }
}

// 删除
async function deleteRegulation(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除「${row.title}」吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.lawsSystem({ id: row.id }, 'delete')
    ElMessage.success('删除成功')
    getList()
  }
  catch (error) {
    if (error !== 'cancel') {
      // 错误提示已在响应拦截器中统一处理
    }
  }
}

// 分页改变
function handlePageChange(page: number) {
  pagination.value.page = page
  getList()
}

function handleSizeChange(size: number) {
  pagination.value.limit = size
  pagination.value.page = 1
  getList()
}

// 格式化状态
function formatStatus(status: string) {
  return status === '现行有效' ? 'success' : 'danger'
}

// 格式化级别
function formatLevel(level: string): 'danger' | 'warning' | 'primary' | 'info' {
  const levelMap: Record<string, 'danger' | 'warning' | 'primary' | 'info'> = {
    法律: 'danger',
    行政法规: 'warning',
    部门规章: 'primary',
    规范性文件: 'info',
  }
  return levelMap[level] || 'info'
}

// 用于防止重复请求的标记
let _isSearchParamsChanging = false

// 监听搜索参数变化 - 不再自动触发查询，而是等待用户点击查询按钮
watch(
  () => props.searchParams,
  () => {
    _isSearchParamsChanging = true
    pagination.value.page = 1
    // 不再自动调用 getList()
    // 延迟重置标记，确保refreshTrigger的watch不会在同一时刻触发
    nextTick(() => {
      setTimeout(() => {
        _isSearchParamsChanging = false
      }, 100)
    })
  },
  { deep: true, immediate: false },
)

// 监听刷新触发器（始终在refreshTrigger变化时触发查询）
watch(
  () => props.refreshTrigger,
  (newVal, oldVal) => {
    // 当refreshTrigger变化且不是初始化时执行，无论searchParams是否在变化
    if (newVal !== oldVal && oldVal !== undefined) {
      getList()
    }
  },
)

// 暴露方法给父组件
defineExpose({
  getList,
  pagination,
  regulationIds,
})
</script>

<template>
  <div class="rounded-lg p-6">
    <!-- 列表头部 -->
    <div class="mb-6 flex items-center justify-between border-b border-gray-200 pb-4">
      <div>
        <h3 class="text-lg text-gray-800 font-semibold">
          {{ props.isIntelligentMode ? '智能推荐法规' : '法规列表' }}
        </h3>
        <div class="mt-1 text-sm text-gray-500">
          {{ props.isIntelligentMode ? `为您推荐 ${pagination.total} 条法规` : `共 ${pagination.total} 条记录` }}
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      class="w-full"
      :header-cell-style="{ color: '#374151', fontWeight: '500' }"
      :row-style="{ transition: 'all 0.2s' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="title" label="法规名称" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <el-link type="primary" :underline="false" style="color: black;" @click="viewDetail(row)">
            {{ row.title }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column prop="department" label="发布部门" width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.department }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="province" label="发布省份" width="100">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.province }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="code" label="文件号" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-sm text-gray-600 font-mono">{{ row.code }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="pubDate" label="发布日期" width="110">
        <template #default="{ row }">
          <span class="text-sm text-gray-600">{{ row.pubDate }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="useDate" label="使用日期" width="110">
        <template #default="{ row }">
          <span class="text-sm text-gray-600">{{ row.useDate }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="isTime" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="formatStatus(row.isTime)" size="small" class="font-medium">
            {{ row.isTime }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="level" label="级别" show-overflow-tooltip width="120">
        <template #default="{ row }">
          <el-tag :type="formatLevel(row.level)" size="small" class="font-medium">
            {{ row.level }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="220" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <div class="button-row">
              <el-button
                v-auth="'regulatoryDatabaseMode/center/edit'"
                size="small"
                type="primary"
                plain
                class="action-btn"
                @click="editRegulation(row)"
              >
                编辑
              </el-button>
              <el-button
                v-auth="'regulatoryDatabaseMode/center/delete'"
                size="small"
                type="danger"
                plain
                class="action-btn"
                @click="deleteRegulation(row)"
              >
                删除
              </el-button>
            </div>

            <div class="button-row">
              <el-button
                type="primary"
                size="small"
                plain
                class="action-btn"
                @click="viewDetail(row)"
              >
                查看详情
              </el-button>
              <el-button
                v-auth="'regulatoryDatabaseMode/center/join'"
                :type="row.isRelated ? 'warning' : 'success'"
                size="small"
                plain
                :loading="subscribingIds.has(row.id)"
                :disabled="subscribingIds.has(row.id)"
                class="action-btn"
                @click="subscribe(row)"
              >
                <template v-if="!subscribingIds.has(row.id)">
                  {{ row.isRelated ? '已加入' : '加入法规库' }}
                </template>
                <template v-else>
                  {{ row.isRelated ? '移除中...' : '加入中...' }}
                </template>
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div v-if="!props.isIntelligentMode" class="mt-6 flex justify-center pt-4">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination-custom"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 使用 Tailwind CSS 原子化样式，保留必要的深度选择器样式 */
:deep(.el-table__row) {
  &:hover {
    background-color: rgba(248, 250, 252, 0.5) !important;
  }
}

:deep(.el-table__header-wrapper) {
  .el-table__header {
    th {
      border-bottom: 1px solid #e5e7eb;
    }
  }
}

:deep(.el-table__body-wrapper) {
  .el-table__row {
    td {
      border-bottom: 1px solid #f3f4f6;
    }
  }
}

/* 操作按钮布局和间距优化 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.button-row {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
  min-width: auto;
}

/* 分页样式 */
.pagination-custom {
  :deep(.el-pagination__total) {
    color: #6b7280;
    font-size: 14px;
  }

  :deep(.el-pagination__sizes) {
    .el-select {
      .el-input {
        .el-input__wrapper {
          border-radius: 6px;
        }
      }
    }
  }

  :deep(.btn-prev),
  :deep(.btn-next) {
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    color: #6b7280;

    &:hover {
      color: #3b82f6;
      border-color: #3b82f6;
    }

    &.disabled {
      color: #d1d5db;
      border-color: #e5e7eb;
    }
  }

  :deep(.el-pager) {
    li {
      border-radius: 6px;
      border: 1px solid #e5e7eb;
      color: #6b7280;
      margin: 0 2px;

      &:hover {
        color: #3b82f6;
        border-color: #3b82f6;
      }

      &.is-active {
        background-color: #3b82f6;
        border-color: #3b82f6;
        color: white;
      }
    }
  }
}
</style>
