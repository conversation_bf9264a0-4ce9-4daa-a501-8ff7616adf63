<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { QuestionFilled, Search } from '@element-plus/icons-vue'
import { set } from 'nprogress'
import assessApi from '@/api/complianceApi/prevention/assess'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'

interface Course {
  id: string | number
  courseName: string
  [key: string]: any
}

interface FormData {
  id: number | null
  courseId: string
  examName: string
  examDescription: string
  examDuration: number
  questionCount: number
  scorePerQuestion: number
  totalScore: number
  passScore: number
  examStatus: string
  examNotice: string
  isEnabled: number
}

// 路由实例
const router = useRouter()
const route = useRoute()

// 表单数据
const formData = reactive<FormData>({
  id: null as number | null,
  courseId: '',
  examName: '',
  examDescription: '',
  examDuration: 60,
  questionCount: 10,
  scorePerQuestion: 10,
  totalScore: 100,
  passScore: 60,
  examStatus: 'PUBLISHED',
  examNotice: '',
  isEnabled: 1,
})

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)

// 是否为编辑模式
const isEdit = ref(false)

// 生成考题弹窗相关
const generateQuestionDialogVisible = ref(false)
const generateQuestionLoading = ref(false)
const savedExamData = ref<any>(null)

// 课程选择弹窗相关
const courseDialogVisible = ref(false)
const courseLoading = ref(false)
const coursePage = ref(1)
const coursePageSize = ref(10)
const courseTotal = ref(0)
const courseList = ref<Course[]>([])
const selectedCourse = ref<Course | null>(null)
const selectedCourseName = ref('')
const courseSearchParams = reactive({
  courseName: null,
  instructor: null,
})

// 课程选择表格的引用
const courseTableRef = ref()

// 表单验证规则
const formRules = {
  examName: [
    { required: true, message: '请输入考试名称', trigger: 'blur' },
  ],
  courseId: [
    { required: true, message: '请选择关联课程', trigger: 'change' },
  ],
  examDuration: [
    { required: true, message: '请输入考试时长', trigger: 'blur' },
  ],
  questionCount: [
    { required: true, message: '请输入题目数量', trigger: 'blur' },
  ],
  scorePerQuestion: [
    { required: true, message: '请输入每题分值', trigger: 'blur' },
  ],
  totalScore: [
    { required: true, message: '请输入总分', trigger: 'blur' },
  ],
  passScore: [
    { required: true, message: '请输入及格分数', trigger: 'blur' },
  ],
  examNotice: [
    { required: true, message: '请输入考核说明', trigger: 'blur' },
  ],
}

// 获取课程列表
async function getCourseList() {
  try {
    courseLoading.value = true
    const queryParams = {
      courseName: courseSearchParams.courseName || null,
      instructor: courseSearchParams.instructor || null,
    }

    const pagingParams = {
      page: coursePage.value - 1,
      size: coursePageSize.value,
    }

    const res = await trainingCurriculum.system(pagingParams, queryParams)

    if (res) {
      let courseData: Course[] = []
      if (res.content && Array.isArray(res.content)) {
        courseData = res.content
        courseTotal.value = res.totalElements || res.total || 0
      }
      else if (Array.isArray(res)) {
        courseData = res
        courseTotal.value = res.length
      }
      else if (res.data) {
        if (res.data.content && Array.isArray(res.data.content)) {
          courseData = res.data.content
          courseTotal.value = res.data.totalElements || res.data.total || 0
        }
        else if (Array.isArray(res.data)) {
          courseData = res.data
          courseTotal.value = res.data.length
        }
      }

      courseList.value = courseData
    }
    else {
      courseList.value = []
      courseTotal.value = 0
    }
  }
  catch (error) {
    console.error('获取课程列表失败:', error)
    // 错误提示已在响应拦截器中统一处理
    courseList.value = []
    courseTotal.value = 0
  }
  finally {
    courseLoading.value = false
  }
}

// 打开课程选择弹窗
function openCourseSelector() {
  courseDialogVisible.value = true
  selectedCourse.value = null
  coursePage.value = 1
  getCourseList()

  // 如果是编辑模式且有选中的课程，设置当前行
  nextTick(() => {
    if (formData.courseId && courseTableRef.value) {
      const currentCourse = courseList.value.find((course: Course) => course.id.toString() === formData.courseId)
      if (currentCourse) {
        courseTableRef.value.setCurrentRow(currentCourse)
        selectedCourse.value = currentCourse
      }
    }
  })
}

// 课程搜索
function searchCourses() {
  coursePage.value = 1
  getCourseList()
}

// 重置课程搜索
function resetCourseSearch() {
  courseSearchParams.courseName = null
  courseSearchParams.instructor = null
  coursePage.value = 1
  getCourseList()
}

// 课程分页变化
function handleCoursePageChange() {
  getCourseList()
}

function handleCourseSizeChange() {
  coursePage.value = 1
  getCourseList()
}

// 课程行点击事件
function handleCourseRowClick(row: Course) {
  selectedCourse.value = row
}

// 确认选择课程
function confirmSelectCourses() {
  if (!selectedCourse.value) {
    ElMessage.warning('请选择一门课程')
    return
  }

  formData.courseId = selectedCourse.value.id.toString()
  selectedCourseName.value = selectedCourse.value.courseName

  // 关闭弹窗并清空选择
  courseDialogVisible.value = false
  selectedCourse.value = null

  ElMessage.success('课程选择成功')
}

// 获取考试详情
async function getExamDetail(id: number) {
  loading.value = true
  try {
    const response = await assessApi.getExamInfo(id)
    if (response.data) {
      Object.assign(formData, response.data)
      // 设置选中的课程名称
      if (formData.courseId) {
        // 先加载课程列表以获取课程名称
        await getCourseList()
        const selectedCourse = courseList.value.find((course: Course) => course.id.toString() === formData.courseId)
        if (selectedCourse) {
          selectedCourseName.value = selectedCourse.courseName
        }
      }
    }
  }
  catch (error: any) {
    console.error('获取考试详情失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }
  finally {
    loading.value = false
  }
}

// 保存考试信息
function saveExam() {
  formRef.value?.validate().then(() => {
    loading.value = true

    const apiCall = isEdit.value && formData.id
      ? assessApi.updateExamInfo(formData.id, formData)
      : assessApi.createExamInfo(formData)

    apiCall.then((response: any) => {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      // 保存返回的数据，用于生成考题
      savedExamData.value = response.data || response
      // 显示生成考题弹窗
      generateQuestionDialogVisible.value = true
    }).catch((error: any) => {
      console.error('保存失败:', error)
      // 错误提示已在响应拦截器中统一处理
    }).finally(() => {
      loading.value = false
    })
  }).catch(() => {
    // 表单验证失败
  })
}

// 生成考题
function generateQuestions() {
  if (generateQuestionLoading.value) { return } // 防抖控制

  generateQuestionLoading.value = true

  const requestData = {
    courseId: formData.courseId,
    examId: savedExamData.value?.id || formData.id,
  }

  assessApi.generateExamQuestion(requestData).then(() => {
    ElMessage.success('考题生成成功')
    generateQuestionDialogVisible.value = false
    router.back()
  }).catch((error: any) => {
    console.error('生成考题失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }).finally(() => {
    generateQuestionLoading.value = false
  })
  setTimeout(() => {
    generateQuestionDialogVisible.value = false
    ElMessage.info('考题生成中请稍后')
    router.back()
  }, 1000)
}

// 取消生成考题
function cancelGenerateQuestions() {
  generateQuestionDialogVisible.value = false
  router.back()
}

// 返回
function goBack() {
  router.back()
}

// 初始化
onMounted(() => {
  const examId = route.query.id
  if (examId) {
    isEdit.value = true
    getExamDetail(Number(examId))
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ isEdit ? '编辑考核' : '新增考核' }}
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-debounce="3000" type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="saveExam">
              保存
            </el-button>
            <el-button v-debounce="3000" class="!rounded-button whitespace-nowrap" @click="goBack">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div class="p-6">
        <!-- 表单卡片 -->
        <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
          <h2 class="mb-6 border-b pb-2 text-lg font-semibold">
            基本信息
          </h2>
          <el-form ref="formRef" :model="formData" :rules="formRules" label-position="right" label-width="120px">
            <el-form-item label="考试名称" prop="examName" required>
              <el-input v-model="formData.examName" placeholder="请输入考试名称" />
            </el-form-item>
            <el-form-item label="考试描述">
              <el-input v-model="formData.examDescription" type="textarea" :rows="3" placeholder="请输入考试描述" />
            </el-form-item>
            <el-form-item label="关联课程" prop="courseId" required>
              <div class="flex items-center gap-2">
                <el-input
                  v-model="selectedCourseName"
                  placeholder="请选择关联课程"
                  readonly
                  class="w-[300px]"
                />
                <el-button v-debounce="3000" type="primary" @click="openCourseSelector">
                  选择课程
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="考核说明" required>
              <el-input
                v-model="formData.examNotice"
                type="textarea"
                :rows="5"
                placeholder="请输入考核说明"
              />
            </el-form-item>
          </el-form>
        </div>
        <!-- 考核设置 -->
        <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
          <h2 class="mb-6 border-b pb-2 text-lg font-semibold">
            考核设置
          </h2>
          <el-form ref="formRef" :model="formData" :rules="formRules" label-position="right" label-width="120px">
            <el-form-item label="考试时长" prop="examDuration" required>
              <div class="flex items-center gap-2">
                <el-input-number
                  v-model="formData.examDuration"
                  :min="1"
                  :max="999"
                  placeholder="请输入时长"
                  class="flex-1"
                />
                <span class="text-gray-500">分钟</span>
              </div>
            </el-form-item>
            <el-form-item label="题目数量" prop="questionCount" required>
              <el-input-number
                v-model="formData.questionCount"
                :min="1"
                :disabled="true"
                :max="100"
                placeholder="请输入题目数量"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="每题分值" prop="scorePerQuestion" required>
              <el-input-number
                v-model="formData.scorePerQuestion"
                :disabled="true"
                :min="1"
                :max="100"
                placeholder="请输入每题分值"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="总分" prop="totalScore" required>
              <el-input-number
                v-model="formData.totalScore"
                :min="1"
                :disabled="true"
                :max="1000"
                placeholder="请输入总分"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="及格分数" prop="passScore" required>
              <el-input-number
                v-model="formData.passScore"
                :min="1"
                :max="1000"
                :disabled="true"
                placeholder="请输入及格分数"
                class="w-full"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </PageMain>

    <!-- 课程选择弹窗 -->
    <el-dialog
      v-model="courseDialogVisible"
      title="选择课程"
      width="70%"
      :close-on-click-modal="false"
    >
      <!-- 搜索区域 -->
      <div class="mb-4">
        <el-form :inline="true" :model="courseSearchParams">
          <el-form-item label="课程名称">
            <el-input
              v-model="courseSearchParams.courseName"
              placeholder="请输入课程名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="讲师">
            <el-input
              v-model="courseSearchParams.instructor"
              placeholder="请输入讲师名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button v-debounce="3000" type="primary" @click="searchCourses">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button v-debounce="3000" @click="resetCourseSearch">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 课程列表表格 -->
      <el-table
        ref="courseTableRef"
        v-loading="courseLoading"
        :data="courseList"
        height="300"
        highlight-current-row
        row-key="id"
        @current-change="handleCourseRowClick"
      >
        <el-table-column prop="courseName" label="课程名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="durationMinutes" label="课程时长" width="120">
          <template #default="{ row }">
            {{ row.durationMinutes || 0 }}分钟
          </template>
        </el-table-column>
        <el-table-column prop="courseType" label="课程类型" width="120" />
        <el-table-column prop="instructor" label="讲师" width="120" />
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="coursePage"
          v-model:page-size="coursePageSize"
          :page-sizes="[5, 10, 20]"
          :total="courseTotal"
          layout="total, sizes, prev, pager, next"
          @current-change="handleCoursePageChange"
          @size-change="handleCourseSizeChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-debounce="3000" @click="courseDialogVisible = false">
            取消
          </el-button>
          <el-button v-debounce="3000" type="primary" :disabled="!selectedCourse" @click="confirmSelectCourses">
            确定选择
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成考题弹窗 -->
    <el-dialog
      v-model="generateQuestionDialogVisible"
      title="生成考题"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="text-center">
        <el-icon class="mb-4 text-4xl text-blue-500">
          <QuestionFilled />
        </el-icon>
        <p class="mb-2 text-lg">
          考试信息保存成功！
        </p>
        <p class="text-gray-600">
          是否立即为此考试生成考题？
        </p>
      </div>

      <template #footer>
        <div class="dialog-footer text-center">
          <el-button v-debounce="3000" :disabled="generateQuestionLoading" @click="cancelGenerateQuestions">
            取消
          </el-button>
          <el-button
            v-debounce="3000"
            type="primary"
            :loading="generateQuestionLoading"
            @click="generateQuestions"
          >
            确定生成
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
/* 课程选择表格选中行样式 */
:deep(.el-table__body tr.current-row) {
  background-color: #e6f7ff !important;
  border: 2px solid #1890ff !important;
}

:deep(.el-table__body tr.current-row td) {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 500 !important;
}

:deep(.el-table__body tr.current-row:hover td) {
  background-color: #bae7ff !important;
}

/* 表格行悬停效果 */
:deep(.el-table__body tr:hover td) {
  background-color: #f5f5f5 !important;
  cursor: pointer;
}
</style>
