import api from '../index'

export default {
  // 获取支付订单列表详情
  getPayOrderList(params: any) {
    return api.post('/whiskerguardorderservice/api/pay/orders/page', params)
  },

  // 获取管理许可证列表
  getLicenseList(params: any) {
    return api.post('/whiskerguardlicenseservice/api/licenses/page', params)
  },
  // 获取管理许可证订阅列表
  getLicenseSubscribeList(params: any) {
    return api.post('/whiskerguardlicenseservice/api/license/subscriptions/page', params)
  },
  // 启用功能模块
  enableModule(params: any) {
    return api.post('/whiskerguardlicenseservice/api/license/subscription/features/page', params)
  },
  // 检查配额
  checkQuota(tenantId: any) {
    return api.get(`/whiskerguardlicenseservice/api/licenses/usage`, {})
  },

  // 套餐与方案

  // 获取套餐列表
  getPackageList(params: any) {
    return api.post('/whiskerguardlicenseservice/api/license/product/packages/page', params)
  },
  // 获取许可证订阅功能特性信息
  getLicenseFeatures(params: any) {
    return api.post('/whiskerguardlicenseservice/api/license/product/package/features/page', params)
  },
  // 创建新的支付订单
  createPayOrder(params: any) {
    return api.post('/whiskerguardorderservice/api/pay/orders', params)
  },
  // 订单列表
  getOrderList(params: any) {
    return api.post('/whiskerguardorderservice/api/pay/orders/page', params)
  },
  // 开始支付
  startPayment(params: any) {
    return api.post('/whiskerguardorderservice/union/pay', params)
  },
  // 支付宝回调
  aliCallback(params: any) {
    return api.post('/whiskerguardorderservice/union/pay/alipay/notify', params)
  },
  // 微信回调
  wechatCallback(params: any) {
    return api.post('/whiskerguardorderservice/union/pay/wechat/notify', params)
  },
  // 轮询查订单详情
  queryOrderDetail(orderId: any) {
    return api.get(`/whiskerguardorderservice/api/pay/orders/${orderId}`, {})
  },
}
