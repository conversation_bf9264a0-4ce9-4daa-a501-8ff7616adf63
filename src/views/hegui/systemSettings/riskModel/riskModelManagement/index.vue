<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import riskModelApi from '@/api/riskModel'
import PageCompon from '@/components/pageComponent/index.vue'
import PageHeader from '@/components/PageHeader/index.vue'
import PageMain from '@/components/PageMain/index.vue'

function handleSelectionChange(_val: any) {
  // 处理选择变化
}

// 状态映射：英文状态码到中文
function getStatusLabel(isDefault: boolean): string {
  return isDefault ? '默认模型' : '非默认模型'
}

function getStatusTag(isDefault: boolean): 'success' | 'info' {
  return isDefault ? 'success' : 'info'
}

const loading = ref(false)
const dataList: any = ref([])
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 筛选表单数据
const searchForm = ref({
  name: '',
  isDefault: '',
  dateRange: [] as any,
})

// 新增/编辑
const router = useRouter()

// 查看风险模型详情
function goDetail(row: any) {
  router.push({
    path: '/systemSettings/riskModel/riskModelManagement/detail',
    query: { id: row.id },
  })
}

// 跳转到模型库
function goModelLibrary() {
  router.push({
    name: '/systemSettings/riskModel/modelLibrary',
  })
}

// 跳转到评估历史
function goEvaluationHistory() {
  router.push({
    name: '/systemSettings/riskModel/evaluationHistory',
  })
}

// 跳转到模型比较
function goModelComparison() {
  router.push({
    name: '/systemSettings/riskModel/modelComparison',
  })
}

onMounted(() => {
  getList()
})

function getList() {
  const params: any = {
    page: Number(paging.value.page) - 1,
    size: Number(paging.value.limit),
  }

  // 添加筛选参数，空字符串转为null
  if (searchForm.value.name && searchForm.value.name.trim() !== '') {
    params.name = searchForm.value.name.trim()
  }
  if (searchForm.value.isDefault && searchForm.value.isDefault !== 'all') {
    params.isDefault = searchForm.value.isDefault === 'true'
  }
  if (searchForm.value.dateRange && Array.isArray(searchForm.value.dateRange) && searchForm.value.dateRange.length === 2) {
    params.createdAtStart = searchForm.value.dateRange[0]
    params.createdAtEnd = searchForm.value.dateRange[1]
  }

  loading.value = true
  riskModelApi.getRiskModels(params).then((res: any) => {
    dataList.value = res.content ? res.content : []
    paging.value.total = res.totalElements ? res.totalElements : 0
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

// 查询方法
function handleSearch() {
  paging.value.page = 1
  getList()
}

// 重置方法
function handleReset() {
  searchForm.value = {
    name: '',
    isDefault: '',
    dateRange: [] as any,
  }
  paging.value.page = 1
  getList()
}

// 新增风险模型
function addModel() {
  router.push({
    path: '/systemSettings/riskModel/riskModelManagement/addEdit',
  })
}

// 编辑风险模型
function editModel(item: any) {
  router.push({
    path: '/systemSettings/riskModel/riskModelManagement/addEdit',
    query: { id: item.id },
  })
}

// 删除风险模型
function deleteModel(item: any) {
  ElMessageBox.confirm(
    `确定要删除风险模型"${item.name}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    riskModelApi.deleteRiskModel(item.id).then((_res: any) => {
      ElMessage.success('风险模型删除成功')
      getList()
    }).catch((error: any) => {
      ElMessage.error('风险模型删除失败')
      console.error(error)
    })
  }).catch(() => {
    // 用户取消删除
  })
}

// 设置默认模型
function setDefaultModel(item: any) {
  ElMessageBox.confirm(
    `确定要将"${item.name}"设置为默认模型吗？`,
    '确认设置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    riskModelApi.setDefaultModel(item.id, { isDefault: true }).then((_res: any) => {
      ElMessage.success('默认模型设置成功')
      getList()
    }).catch((error: any) => {
      ElMessage.error('默认模型设置失败')
      console.error(error)
    })
  }).catch(() => {
    // 用户取消操作
  })
}
</script>

<template>
  <div class="absolute-container">
    <PageHeader title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-xl c-[#000000] font-bold">
              风险模型管理
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['riskModel/index/add']" type="primary" class="!rounded-button whitespace-nowrap" @click="addModel">
              新增风险模型
            </el-button>
            <!-- <el-button v-auth="['riskModel/index/library']" type="primary" plain class="!rounded-button whitespace-nowrap" @click="goModelLibrary">
              模型库
            </el-button>
            <el-button v-auth="['riskModel/index/history']" type="success" class="!rounded-button whitespace-nowrap" @click="goEvaluationHistory">
              评估历史
            </el-button>
            <el-button v-auth="['riskModel/index/comparison']" type="success" plain class="!rounded-button whitespace-nowrap" @click="goModelComparison">
              模型比较
            </el-button> -->
          </div>
        </div>
      </template>
    </PageHeader>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="mt-10">
          <el-col :span="24">
            <el-card shadow="never">
              <div class="flex items-center space-x-4">
                <el-input v-model="searchForm.name" placeholder="请输入模型名称" class="w-64" />
                <!-- <el-select v-model="searchForm.isDefault" clearable placeholder="模型类型" class="w-32">
                  <el-option label="默认模型" value="true" />
                  <el-option label="普通模型" value="false" />
                </el-select> -->
                <el-date-picker
                  v-model="searchForm.dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  class="w-64"
                />
                <el-button type="primary" @click="handleSearch">
                  查询
                </el-button>
                <el-button @click="handleReset">
                  重置
                </el-button>
              </div>
              <el-table v-loading="loading" :data="dataList" style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="50" />
                <el-table-column prop="name" label="模型名称" width="200">
                  <template #default="{ row }">
                    {{ row.name }}
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="模型描述" width="250">
                  <template #default="{ row }">
                    {{ row.description || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="isDefault" label="模型类型" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTag(row.isDefault)" size="small">
                      {{ getStatusLabel(row.isDefault) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveFrom" label="生效时间" width="180">
                  <template #default="{ row }">
                    {{ row.effectiveFrom || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveTo" label="失效时间" width="180">
                  <template #default="{ row }">
                    {{ row.effectiveTo || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="createdBy" label="创建人" width="120" />
                <el-table-column prop="createdAt" label="创建时间" width="180" />
                <el-table-column width="280" label="操作">
                  <template #default="{ row }">
                    <el-button v-auth="['riskModel/index/view']" size="small" type="success" plain :underline="false" class="mr-2" @click="goDetail(row)">
                      查看
                    </el-button>
                    <el-button v-auth="['riskModel/index/edit']" size="small" type="primary" plain :underline="false" class="mr-2" @click="editModel(row)">
                      编辑
                    </el-button>
                    <!-- <el-button
                      v-if="!row.isDefault"
                      v-auth="['riskModel/index/setDefault']"
                      size="small"
                      type="success" plain
                      :underline="false"
                      class="mr-2"
                      @click="setDefaultModel(row)"
                    >
                      设为默认
                    </el-button> -->
                    <el-button
                      v-auth="['riskModel/index/delete']"
                      size="small"
                      type="danger" plain
                      :underline="false"
                      @click="deleteModel(row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <PageCompon
                :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                @pag-change="Object.assign(paging, $event), getList()"
              />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
.absolute-container {
  height: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
