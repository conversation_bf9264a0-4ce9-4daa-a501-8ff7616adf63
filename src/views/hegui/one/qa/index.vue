<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowUp, ChatDotRound, Close, Document, Paperclip, Refresh, Upload, UploadFilled } from '@element-plus/icons-vue'
import qaApi from '@/api/complianceApi/one/qa'
import useUserStore from '@/store/modules/user'
import PageHeader from '@/components/PageHeader/index.vue'
import PageMain from '@/components/PageMain/index.vue'
import MarkdownRenderer from '@/components/MarkdownRenderer/index.vue'
import uploadApi from '@/api/mbbUpload'

// 用户信息
const userStore = useUserStore()

// 响应式数据
const currentModelId = ref('')
const modelList = ref([] as any[])
const chatMessages = ref([] as any[])
const inputMessage = ref('')
const isAnswering = ref(false)
const conversationId = ref('')

// 历史聊天记录相关
const historyMessages = ref([] as any[])
const loadingHistory = ref(true)
const loadingMoreHistory = ref(false)
const currentHistoryPage = ref(0)
const historyPageSize = ref(10)
const hasMoreHistory = ref(true)

// 文件管理 - 改为当前消息的文件
const currentFiles = ref([] as any[])
const uploading = ref(false)

// 上传成功的文件key列表，用于发送给AI
const uploadedFileKeys = ref([] as any[])

// DOM引用
const messagesContainer = ref()
const fileInputRef = ref()

// 图片预览相关
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 头像处理相关
const avatarError = ref(false)
const avatarUrl = ref('')

// 获取头像URL
async function getAvatarUrl(key: string) {
  try {
    const response = await uploadApi.getFileUrl(key)
    return response || ''
  }
  catch (error) {
    console.error('获取头像URL失败:', error)
    return ''
  }
}

// 计算属性
const userAvatar = computed(() => {
  if (avatarUrl.value && !avatarError.value) {
    return avatarUrl.value
  }
  // 默认头像 - 使用项目中的默认头像图片
  return new URL('/src/assets/images/headimg.png', import.meta.url).href
})

const aiAvatar = computed(() => 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png')

// 监听用户头像变化
async function updateUserAvatar() {
  try {
    const userDetail = userStore.avatar
    if (userDetail) {
      // 如果avatar是完整的URL，直接使用
      if (userDetail.startsWith('http')) {
        avatarUrl.value = userDetail
      }
      else {
        // 否则通过接口获取文件URL
        avatarUrl.value = await getAvatarUrl(userDetail)
      }
    }
    else {
      avatarUrl.value = ''
    }
    if (avatarError.value) {
      avatarError.value = false
    }
  }
  catch (error) {
    console.error('更新用户头像失败:', error)
    avatarUrl.value = ''
  }
}

// 当前文件的ID列表，用逗号分隔
const _currentFileIds = computed(() => {
  return uploadedFileKeys.value.join(',')
})

// 方法
async function loadModelList() {
  try {
    const res = await qaApi.getModelList()
    if (res && res.length > 0) {
      modelList.value = res.filter((model: any) => model.status === 'AVAILABLE')
      if (modelList.value.length > 0) {
        // 默认选择第一个模型
        currentModelId.value = modelList.value[0].id
      }
    }
  }
  catch (error) {
    // 错误提示已在响应拦截器中统一处理
  }
}

async function getConversationId() {
  try {
    const res = await qaApi.getSessionId()
    conversationId.value = res?.conversationId || ''
  }
  catch (error) {
    // 错误提示已在响应拦截器中统一处理
  }
}

// 文件上传相关方法
function triggerFileUpload() {
  fileInputRef.value?.click()
}

async function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files || files.length === 0) {
    return
  }

  uploading.value = true

  try {
    for (const file of files) {
      // 验证文件
      if (!validateFile(file)) {
        continue
      }

      // 上传文件
      const res = await qaApi.uploadFile(file)

      // 保存文件信息用于显示
      currentFiles.value.push({
        id: res.fileId || res.key || res.data?.key, // 兼容不同的返回格式
        name: file.name,
        size: file.size,
        type: getFileType(file.name),
      })

      // 保存文件key用于发送给AI
      const fileKey = res.fileId || res.key || res.data?.key
      if (fileKey) {
        uploadedFileKeys.value.push(fileKey)
      }
    }

    ElMessage.success(`成功上传 ${files.length} 个文件`)
  }
  catch (error) {
    console.error('文件上传失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }
  finally {
    uploading.value = false
    // 清空文件输入
    if (target) {
      target.value = ''
    }
  }
}

function validateFile(file: File): boolean {
  const validTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/markdown',
    'application/javascript',
    'text/javascript',
    'application/json',
    // 图片格式
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/svg+xml',
  ]

  if (!validTypes.includes(file.type)) {
    ElMessage.error(`不支持的文件格式: ${file.name}`)
    return false
  }

  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    ElMessage.error(`文件大小不能超过 10MB: ${file.name}`)
    return false
  }

  return true
}

function removeFile(index: number) {
  currentFiles.value.splice(index, 1)
  uploadedFileKeys.value.splice(index, 1) // 同时移除对应的文件key
  ElMessage.success('文件已移除')
}

function getFileType(fileName: string) {
  const ext = fileName.split('.').pop()?.toLowerCase()
  if (ext && ['pdf'].includes(ext)) {
    return 'pdf'
  }
  if (ext && ['doc', 'docx'].includes(ext)) {
    return 'doc'
  }
  if (ext && ['ppt', 'pptx'].includes(ext)) {
    return 'ppt'
  }
  if (ext && ['xls', 'xlsx'].includes(ext)) {
    return 'xls'
  }
  if (ext && ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(ext)) {
    return 'image'
  }
  return 'txt'
}

function formatFileSize(bytes: number) {
  if (bytes === 0) {
    return '0 B'
  }
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

// 预览文件
async function previewFile(file: any) {
  try {
    // 先获取真实的文件地址
    const realUrl = await uploadApi.getFileUrl(file.id)

    // 根据文件类型决定预览方式
    if (file.type === 'image') {
      // 图片文件显示预览弹窗
      previewImageUrl.value = realUrl
      imagePreviewVisible.value = true
    }
    else if (file.type === 'pdf') {
      // PDF文件在新窗口打开
      window.open(realUrl, '_blank')
    }
    else {
      // 其他文件类型直接下载
      const link = document.createElement('a')
      link.href = realUrl
      link.download = file.name
      link.click()
    }
  }
  catch (error) {
    console.error('获取文件地址失败:', error)
    ElMessage.error('文件预览失败，请稍后重试')
  }
}

// 关闭图片预览
function closeImagePreview() {
  imagePreviewVisible.value = false
  previewImageUrl.value = ''
}

async function sendMessage() {
  if (!inputMessage.value.trim() || isAnswering.value) {
    return
  }

  const userMessage = {
    isUser: true,
    name: (() => {
      try {
        const userDetail = JSON.parse(localStorage.getItem('userDetail') || '{}')
        return userDetail.realName || '用户'
      }
      catch {
        return '用户'
      }
    })(),
    content: inputMessage.value,
    time: new Date().toLocaleString(),
    files: [...currentFiles.value], // 保存当前消息的文件
  }

  chatMessages.value.push(userMessage)
  const question = inputMessage.value
  const filesForApi = [...currentFiles.value] // 保存文件信息用于API调用

  // 清空输入和文件
  inputMessage.value = ''
  currentFiles.value = []
  uploadedFileKeys.value = [] // 清空文件keys

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 开始AI回答
  await doAnswer(question, filesForApi)
}

async function doAnswer(question: string, files: any[] = []) {
  isAnswering.value = true

  // 添加思考中的消息
  const thinkingMessage = {
    isUser: false,
    name: '猫伯伯',
    content: '思考中...',
    time: new Date().toLocaleString(),
  }
  chatMessages.value.push(thinkingMessage)

  await nextTick()
  scrollToBottom()

  try {
    // 构建附件元数据 - 按照新的格式要求
    let metadataStr = null
    if (files.length > 0) {
      const cosKeys = files.map(file => file.id).join(',')
      const fileNames = files.map(file => file.name).join(',')

      const metadataObj = {
        cosKey: cosKeys,
        fileName: fileNames,
      }
      // metadataStr = JSON.stringify(metadataObj)
      metadataStr = metadataObj
    }

    const params = {
      toolKey: 'kimi',
      prompt: question,
      employeeId: userStore.userId,
      streaming: true,
      conversationId: conversationId.value,
      metadata: metadataStr,
    }

    const response = await qaApi.queryChatStream(params)

    // 处理流式响应
    let fullAnswer = ''
    let displayedAnswer = ''

    // 流式输出效果
    const processStreamData = (data: string) => {
      fullAnswer += data

      // 打字机效果
      const typewriterEffect = () => {
        if (displayedAnswer.length < fullAnswer.length) {
          displayedAnswer += fullAnswer[displayedAnswer.length]
          chatMessages.value[chatMessages.value.length - 1].content = displayedAnswer
          // 在打字机效果过程中保持滚动到底部
          nextTick(() => {
            scrollToBottom()
          })
          setTimeout(typewriterEffect, 30)
        }
        else {
          isAnswering.value = false
          // 回答完成后确保滚动到底部
          nextTick(() => {
            scrollToBottom()
          })
        }
      }

      typewriterEffect()
    }

    // 处理实际的API响应
    chatMessages.value[chatMessages.value.length - 1].content = ''

    // 检查响应数据格式并处理
    if (response) {
      try {
        // 如果是字符串格式的SSE响应
        if (typeof response === 'string') {
          // 解析SSE格式的数据
          const lines = response.split('\n')
          let combinedContent = ''

          for (const line of lines) {
            if (line.startsWith('data:')) {
              try {
                const jsonStr = line.substring(5).trim()
                if (jsonStr) {
                  const data = JSON.parse(jsonStr)
                  if (data.content) {
                    combinedContent += data.content
                  }
                }
              }
              catch (parseError) {
                console.warn('解析SSE数据失败:', parseError)
              }
            }
          }

          if (combinedContent) {
            processStreamData(combinedContent)
          }
          else {
            processStreamData('收到响应但内容为空')
          }
        }
        else if (response.content) {
          processStreamData(response.content)
        }
        else {
          processStreamData(JSON.stringify(response))
        }
      }
      catch (error) {
        processStreamData('响应数据处理失败，请重试')
      }
    }
    else {
      processStreamData('抱歉，没有收到有效的回答，请重试。')
    }
  }
  catch (error) {
    chatMessages.value[chatMessages.value.length - 1].content = '抱歉，回答出现错误，请重试。'
    isAnswering.value = false
    // 错误提示已在响应拦截器中统一处理
  }
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 历史聊天记录相关方法
async function loadChatHistory(isLoadMore = false) {
  if (isLoadMore) {
    loadingMoreHistory.value = true
  }
  else {
    loadingHistory.value = true
    // 重置分页状态
    currentHistoryPage.value = 0
    hasMoreHistory.value = true
    historyMessages.value = []
  }

  try {
    const params = {
      employeeId: userStore.userId,
      status: 'SUCCESS',
      page: currentHistoryPage.value,
      size: historyPageSize.value,
    }

    const res = await qaApi.queryChatList(params)
    if (res && Array.isArray(res)) {
      const historyData = res

      if (historyData.length > 0) {
        // 转换历史数据为聊天消息格式
        const newHistoryMessages = convertHistoryToMessages(historyData)

        if (isLoadMore) {
          // 加载更多时，将新消息插入到现有历史消息的前面
          historyMessages.value = [...newHistoryMessages, ...historyMessages.value]
        }
        else {
          // 首次加载
          historyMessages.value = newHistoryMessages
        }

        // 更新聊天消息列表
        updateChatMessages()

        // 增加页码
        currentHistoryPage.value++

        // 判断是否还有更多数据：如果返回的数据量小于请求的页面大小，说明没有更多了
        hasMoreHistory.value = historyData.length >= historyPageSize.value
      }
      else {
        // 返回空数组，说明没有更多历史记录了
        hasMoreHistory.value = false
      }
    }
    else {
      hasMoreHistory.value = false
    }
  }
  catch (error) {
    console.error('加载历史聊天记录失败:', error)
    // 错误提示已在响应拦截器中统一处理
    hasMoreHistory.value = false
  }
  finally {
    if (isLoadMore) {
      loadingMoreHistory.value = false
    }
    else {
      loadingHistory.value = false
    }
  }
}

// 更新聊天消息列表
function updateChatMessages() {
  // 获取当前会话消息（非历史消息）
  const currentSessionMessages = chatMessages.value.filter((msg: any) => !msg.isHistory)
  // 合并历史消息和当前会话消息，历史消息在前
  chatMessages.value = [...historyMessages.value, ...currentSessionMessages]
}

// 加载更多历史记录
async function loadMoreHistory() {
  if (!hasMoreHistory.value || loadingMoreHistory.value) {
    return
  }

  // 保存当前滚动位置
  const container = messagesContainer.value
  const scrollHeight = container?.scrollHeight || 0

  await loadChatHistory(true)

  // 恢复滚动位置，避免跳动
  nextTick(() => {
    if (container) {
      const newScrollHeight = container.scrollHeight
      const scrollDiff = newScrollHeight - scrollHeight
      container.scrollTop = container.scrollTop + scrollDiff
    }
  })
}

// 转换历史数据为聊天消息格式
function convertHistoryToMessages(historyData: any[]) {
  const messages: any[] = []

  // 按时间排序
  const sortedHistory = historyData.sort((a, b) =>
    new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  )

  for (const item of sortedHistory) {
    const message = {
      isUser: item.isUser,
      name: item.isUser
        ? (() => {
            try {
              const userDetail = JSON.parse(localStorage.getItem('userDetail') || '{}')
              return userDetail.realName || '用户'
            }
            catch {
              return '用户'
            }
          })()
        : '猫伯伯',
      content: item.content.replace(/\\n/g, '\n'), // 处理换行符
      time: formatHistoryTime(item.createdAt),
      isHistory: true, // 标记为历史消息
    }
    messages.push(message)
  }

  return messages
}

// 格式化历史消息时间
function formatHistoryTime(timestamp: string) {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 刷新历史记录
async function refreshHistory() {
  await loadChatHistory()
  // 刷新后滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 生命周期
onMounted(async () => {
  await getConversationId()
  await loadModelList()
  await updateUserAvatar() // 初始化用户头像
  await loadChatHistory() // 加载历史聊天记录

  // 页面加载完成后滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})
</script>

<template>
  <div class="absolute-container">
    <!-- 页面头部 -->
    <PageHeader>
      <template #content>
        <div class="w-full flex items-center justify-between">
          <div class="flex flex-col">
            <h1 class="mb-1 text-xl text-gray-800 font-bold">
              AI智能问答
            </h1>
          </div>
          <!-- 模型选择器和历史记录按钮 -->
          <div class="flex items-center gap-3">
            <!-- <el-button
              type="primary"
              :icon="ChatDotRound"
              plain
              @click="toggleHistory"
            >
              {{ historyVisible ? '隐藏历史' : '历史记录' }}
            </el-button> -->

            <!-- <span class="text-sm text-gray-700 font-medium">AI模型:</span>
            <el-select
              v-model="currentModelId"
              placeholder="选择AI模型"
              class="w-48"
              @change="onModelChange"
            >
              <el-option
                v-for="model in modelList"
                :key="model.id"
                :label="model.name"
                :value="model.id"
              />
            </el-select> -->

            <el-button
              type="primary"
              :icon="Refresh"
              size="small"
              plain
              :loading="loadingHistory"
              @click="refreshHistory"
            >
              刷新历史
            </el-button>
          </div>
        </div>
      </template>
    </PageHeader>

    <!-- 主要内容区域 -->
    <PageMain>
      <div class="h-[calc(100vh-160px)] min-w-0 flex overflow-hidden">
        <!-- 聊天区域 -->
        <div class="min-w-0 flex flex-1 flex-col overflow-hidden border border-gray-200 rounded-xl bg-white shadow-lg">
          <!-- 聊天区域头部 -->
          <div v-if="false" class="flex items-center justify-between border-b border-gray-200 bg-gray-50 p-4">
            <h3 class="text-lg text-gray-800 font-semibold">
              智能对话
            </h3>
            <el-button
              type="primary"
              :icon="Refresh"
              size="small"
              plain
              :loading="loadingHistory"
              @click="refreshHistory"
            >
              刷新历史
            </el-button>
          </div>

          <!-- 聊天消息区域 -->
          <div ref="messagesContainer" class="flex-1 overflow-y-auto p-4 space-y-6">
            <!-- 历史记录加载状态 -->
            <div v-if="loadingHistory" class="flex justify-center py-8">
              <div class="flex flex-col items-center space-y-3">
                <el-icon class="animate-spin text-2xl text-blue-500">
                  <Refresh />
                </el-icon>
                <span class="text-sm text-gray-500">正在加载历史聊天记录...</span>
              </div>
            </div>

            <!-- 加载更多历史记录按钮 -->
            <div v-if="hasMoreHistory && historyMessages.length > 0 && !loadingHistory" class="flex justify-center">
              <el-button
                type="primary"
                plain
                size="small"
                :loading="loadingMoreHistory"
                @click="loadMoreHistory"
              >
                <template #icon>
                  <el-icon v-if="!loadingMoreHistory">
                    <ArrowUp />
                  </el-icon>
                </template>
                {{ loadingMoreHistory ? '加载中...' : '加载更多历史记录' }}
              </el-button>
            </div>

            <div v-if="chatMessages.length === 0 && !loadingHistory" class="h-full flex flex-col items-center justify-center text-center space-y-4">
              <div class="text-6xl text-gray-300">
                💬
              </div>
              <div class="space-y-2">
                <h3 class="text-lg text-gray-800 font-semibold">
                  开始与AI助手对话吧！
                </h3>
                <p class="text-sm text-gray-500">
                  您可以上传文档并基于文档内容进行问答
                </p>
              </div>
            </div>

            <template v-for="(message, index) in chatMessages" :key="index">
              <!-- 历史消息和当前消息的分隔线 -->
              <div v-if="index === historyMessages.length && historyMessages.length > 0" class="my-6 flex items-center justify-center">
                <div class="h-px flex-1 bg-blue-200" />
                <span class="bg-white px-4 text-sm text-blue-600 font-medium">当前会话</span>
                <div class="h-px flex-1 bg-blue-200" />
              </div>

              <div
                class="mb-6 flex animate-fade-in-up"
                :class="{
                  'flex-row-reverse': message.isUser,
                  'flex-row': !message.isUser,
                }"
              >
                <div class="flex-shrink-0">
                  <img
                    v-if="!message.isUser || (message.isUser && userAvatar && !avatarError)"
                    :src="message.isUser ? userAvatar : aiAvatar"
                    :alt="message.name"
                    class="h-10 w-10 rounded-full"
                    :class="{ 'opacity-70': message.isHistory }"
                    @error="message.isUser && (avatarError = true)"
                  >
                  <!-- 用户头像加载失败时显示默认头像 -->
                  <div
                    v-else-if="message.isUser && avatarError"
                    class="h-10 w-10 flex items-center justify-center rounded-full bg-gray-200"
                    :class="{ 'opacity-70': message.isHistory }"
                  >
                    <svg class="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                    </svg>
                  </div>
                </div>
                <div class="max-w-[70%] min-w-0 flex-1 break-words" :class="message.isUser ? 'mr-3 flex flex-col items-end' : 'ml-3'">
                  <div class="mb-2 flex items-center gap-2" :class="message.isUser ? 'justify-end' : 'justify-start'">
                    <span class="text-sm font-medium" :class="message.isHistory ? 'text-gray-600' : 'text-gray-800'">{{ message.name }}</span>
                    <span class="text-xs text-gray-400">{{ message.time }}</span>
                    <span v-if="message.isHistory" class="rounded-full bg-blue-50 px-2 py-0.5 text-xs text-blue-500">历史</span>
                  </div>

                  <!-- 显示用户消息的文件 -->
                  <div v-if="message.isUser && message.files && message.files.length > 0" class="mb-3 border-l-4 border-indigo-500 rounded-lg bg-gray-50 p-3">
                    <div class="mb-2 text-xs text-indigo-600 font-medium">
                      📎 附件文件:
                    </div>
                    <div class="flex flex-wrap gap-1.5">
                      <el-tag
                        v-for="file in message.files"
                        :key="file.id"
                        type="info"
                        size="small"
                        class="cursor-pointer border-0 bg-indigo-500 text-white transition-all duration-300 hover:bg-indigo-600 hover:shadow-indigo-200 hover:shadow-md hover:-translate-y-0.5"
                        @click="previewFile(file)"
                      >
                        {{ file.name }}
                      </el-tag>
                    </div>
                  </div>

                  <div
                    class="box-border max-w-fit break-words rounded-xl px-3 py-2 leading-relaxed"
                    :class="message.isUser
                      ? (message.isHistory
                        ? 'bg-blue-400 text-white shadow-sm opacity-80'
                        : 'bg-blue-500 text-white shadow-md shadow-blue-200')
                      : (message.isHistory
                        ? 'bg-gray-100 text-gray-700 border border-gray-300 opacity-80'
                        : 'bg-gradient-to-br from-gray-50 to-white text-gray-800 border border-gray-200 shadow-sm')"
                  >
                    <!-- AI消息使用Markdown渲染 -->
                    <MarkdownRenderer v-if="!message.isUser" :content="message.content" />
                    <!-- 用户消息直接显示，处理换行符 -->
                    <div v-else class="whitespace-pre-wrap break-words">
                      {{ message.content }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>

          <!-- 当前选择的文件预览 -->
          <div v-if="currentFiles.length > 0" class="mx-5 my-4 border-2 border-indigo-500 rounded-xl border-dashed bg-gray-50 p-4">
            <div class="mb-3 text-sm text-indigo-600 font-medium">
              <span>📎 待发送文件 ({{ currentFiles.length }})</span>
            </div>
            <div class="flex flex-col gap-2">
              <div
                v-for="(file, index) in currentFiles"
                :key="file.id"
                class="flex items-center justify-between border border-gray-200 rounded-lg bg-white p-2"
              >
                <div class="flex flex-1 flex-col">
                  <span class="text-sm text-gray-800 font-medium">{{ file.name }}</span>
                  <span class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</span>
                </div>
                <div class="flex items-center gap-2">
                  <el-button
                    type="primary"
                    size="small"
                    plain
                    @click="previewFile(file)"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    :icon="Close"
                    circle
                    @click="removeFile(index)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="border-t border-gray-200 from-gray-50 to-white bg-gradient-to-br p-5">
            <div class="mb-3 flex items-end gap-3">
              <!-- 隐藏的文件输入 -->
              <input
                ref="fileInputRef"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.md,.js,.json,.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg"
                style="display: none"
                @change="handleFileSelect"
              >

              <!-- 文件上传按钮 -->
              <el-button
                type="primary"
                :icon="Paperclip"
                :loading="uploading"
                class="h-10 w-10 flex-shrink-0 border border-gray-300 bg-white text-gray-600 duration-200"
                title="上传文件"
                @click="triggerFileUpload"
              />

              <!-- 输入框 -->
              <el-input
                v-model="inputMessage"
                type="textarea"
                :rows="3"
                placeholder="输入您的问题..."
                :disabled="isAnswering"
                class="flex-1"
                @keydown.ctrl.enter="sendMessage"
              />

              <!-- 发送按钮 -->
              <el-button
                type="primary"
                :loading="isAnswering"
                :disabled="!inputMessage.trim()"
                class="h-10 min-w-20 flex-shrink-0 border border-blue-500 bg-blue-500 text-white font-medium transition-colors duration-200 disabled:border-gray-300 hover:border-blue-600 disabled:bg-gray-300 hover:bg-blue-600 disabled:text-gray-500"
                @click="sendMessage"
              >
                {{ isAnswering ? '回答中...' : '发送' }}
              </el-button>
            </div>

            <div class="mt-2 flex items-center justify-between text-xs text-gray-400">
              <span class="flex items-center gap-1">按 Ctrl + Enter 快速发送</span>
              <span class="flex items-center gap-1">支持 PDF、Word、Excel、PPT、图片、文本等格式</span>
            </div>
          </div>
        </div>
      </div>
    </PageMain>

    <!-- 图片预览弹窗 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="80%"
      :before-close="closeImagePreview"
    >
      <div class="min-h-96 flex items-center justify-center p-5 text-center">
        <img
          :src="previewImageUrl"
          alt="预览图片"
          class="max-h-[70vh] max-w-full rounded-lg object-contain shadow-lg"
        >
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
  @use "@/styles/toolsCss";
/* 保留动画效果 */
.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Markdown内容样式优化 */
:deep(.markdown-renderer) {
  @apply max-w-full overflow-x-auto break-words;

  pre {
    @apply max-w-full overflow-x-auto whitespace-pre-wrap break-words;
  }

  table {
    @apply max-w-full break-words;
    table-layout: fixed;
  }

  code {
    @apply break-words;
  }

  img {
    @apply max-w-full h-auto;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .h-\[calc\(100vh-160px\)\] {
    height: calc(100vh - 120px);
  }
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  @apply w-1.5;
}

.overflow-y-auto::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
</style>
