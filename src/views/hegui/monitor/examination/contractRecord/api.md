---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 08-合同管理服务/审查备案

## GET 分页查询所有审查备案记录

GET /whiskerguardcontractservice/api/review/record/filings

描述：分页查询所有审查备案记录。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "objectId": 0,
      "name": "",
      "reviewType": "",
      "content": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageReviewRecordFilingDTO](#schemaresponseentitypagereviewrecordfilingdto)|

## GET 根据ID查询单个审查备案记录

GET /whiskerguardcontractservice/api/review/record/filings/{id}

描述：根据ID查询单个审查备案记录。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |审查备案记录ID|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "objectId": 0,
  "name": "",
  "reviewType": "",
  "content": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityReviewRecordFilingDTO](#schemaresponseentityreviewrecordfilingdto)|

## GET 审查备案

GET /whiskerguardcontractservice/api/review/record/filings/handle

描述：处理审查备案记录。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|objectId|query|integer| 是 |审查对象ID|
|tenantId|query|integer| 是 |租户ID|
|reviewType|query|string| 是 |审查类型|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_ReviewRecordFilingDTO">ReviewRecordFilingDTO</h2>

<a id="schemareviewrecordfilingdto"></a>
<a id="schema_ReviewRecordFilingDTO"></a>
<a id="tocSreviewrecordfilingdto"></a>
<a id="tocsreviewrecordfilingdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "objectId": 0,
  "name": "string",
  "reviewType": "CONTRACT",
  "content": "string",
  "metadata": "string",
  "version": 1,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|objectId|integer(int64)|true|none||备案对象ID，关联到具体的审查记录|
|name|string|true|none||备案名称|
|reviewType|string|false|none||备案对象类型：合同、重大决策、其他审查|
|content|string|false|none||备案内容，存储详细的备案信息|
|metadata|string|false|none||补充字段，JSON格式存储扩展信息|
|version|integer|false|none||当前版本号，默认为1|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|reviewType|CONTRACT|
|reviewType|DECISION|
|reviewType|SUPPLEMENTAL|
|reviewType|VIOLATION|
|reviewType|INVESTIGATE_TASK|
|reviewType|INVESTIGATE_RECORD|
|reviewType|INVESTIGATE_REPORT|
|reviewType|RESPONSIBILITY_CORRECTION|
|reviewType|RESPONSIBILITY_DEAL|
|reviewType|CONTINUOUS_EXPERIENCE|
|reviewType|CONTINUOUS_IMPROVE|
|reviewType|CONTINUOUS_REPORT|
|reviewType|REGULATORY|
|reviewType|ENTERPRISE|
|reviewType|COMPLIANCE_CASE|
|reviewType|COMPLIANCE_OBLIGATION|

<h2 id="tocS_ResponseEntityPageReviewRecordFilingDTO">ResponseEntityPageReviewRecordFilingDTO</h2>

<a id="schemaresponseentitypagereviewrecordfilingdto"></a>
<a id="schema_ResponseEntityPageReviewRecordFilingDTO"></a>
<a id="tocSresponseentitypagereviewrecordfilingdto"></a>
<a id="tocsresponseentitypagereviewrecordfilingdto"></a>

```json
{
  "content": "new ArrayList<>()",
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE"
      }
    ]
  },
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE"
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[ReviewRecordFilingDTO](#schemareviewrecordfilingdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||Returns whether the current{@link Streamable} is empty.|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

<h2 id="tocS_ResponseEntityReviewRecordFilingDTO">ResponseEntityReviewRecordFilingDTO</h2>

<a id="schemaresponseentityreviewrecordfilingdto"></a>
<a id="schema_ResponseEntityReviewRecordFilingDTO"></a>
<a id="tocSresponseentityreviewrecordfilingdto"></a>
<a id="tocsresponseentityreviewrecordfilingdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "objectId": 0,
  "name": "string",
  "reviewType": "CONTRACT",
  "content": "string",
  "metadata": "string",
  "version": 1,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|objectId|integer(int64)|true|none||备案对象ID，关联到具体的审查记录|
|name|string|true|none||备案名称|
|reviewType|string|false|none||备案对象类型：合同、重大决策、其他审查|
|content|string|false|none||备案内容，存储详细的备案信息|
|metadata|string|false|none||补充字段，JSON格式存储扩展信息|
|version|integer|false|none||当前版本号，默认为1|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|reviewType|CONTRACT|
|reviewType|DECISION|
|reviewType|SUPPLEMENTAL|
|reviewType|VIOLATION|
|reviewType|INVESTIGATE_TASK|
|reviewType|INVESTIGATE_RECORD|
|reviewType|INVESTIGATE_REPORT|
|reviewType|RESPONSIBILITY_CORRECTION|
|reviewType|RESPONSIBILITY_DEAL|
|reviewType|CONTINUOUS_EXPERIENCE|
|reviewType|CONTINUOUS_IMPROVE|
|reviewType|CONTINUOUS_REPORT|
|reviewType|REGULATORY|
|reviewType|ENTERPRISE|
|reviewType|COMPLIANCE_CASE|
|reviewType|COMPLIANCE_OBLIGATION|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

