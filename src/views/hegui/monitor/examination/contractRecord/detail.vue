<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  AlarmClock as ElIconAlarmClock,
  Bell as ElIconBell,
  Check as ElIconCheck,
  Document as ElIconDocument,
  Edit as ElIconEdit,
  Star as ElIconStar,
  Upload as ElIconUpload,
  User as ElIconUser,
  Warning as ElIconWarning,
  Promotion,
  VideoPlay,
} from '@element-plus/icons-vue'
import contractRecordApi from '@/api/monitor/contractRecord'
import UploadMbb from '@/components/uploadMbb/index.vue'
import RichTextViewer from '@/components/richText/viewer.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 加载状态
const loading = ref(false)

// 详情数据
const detailData = ref<any>(null)

// 获取详情数据
async function getDetailData() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少备案ID参数')
    router.back()
    return
  }

  try {
    loading.value = true
    const response = await contractRecordApi.getFilingDetail(Number(id))
    if (response) {
      detailData.value = response

      // 解析metadata字段
      if (detailData.value.metadata) {
        try {
          if (typeof detailData.value.metadata === 'string') {
            detailData.value.metadata = JSON.parse(detailData.value.metadata)
          }
        }
        catch (e) {
          console.warn('解析metadata失败:', e)
          detailData.value.metadata = {}
        }
      }
      else {
        detailData.value.metadata = {}
      }
    }
  }
  catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
  finally {
    loading.value = false
  }
}

// 获取审查类型标签
function getReviewTypeLabel(type: string): string {
  const typeLabels: Record<string, string> = {
    CONTRACT: '合同',
    DECISION: '重大决策',
    SUPPLEMENTAL: '补充审查',
    VIOLATION: '违规',
    INVESTIGATE_TASK: '调查任务',
    INVESTIGATE_RECORD: '调查记录',
    INVESTIGATE_REPORT: '调查报告',
    RESPONSIBILITY_CORRECTION: '责任纠正',
    RESPONSIBILITY_DEAL: '责任处理',
    CONTINUOUS_EXPERIENCE: '持续经验',
    CONTINUOUS_IMPROVE: '持续改进',
    CONTINUOUS_REPORT: '持续报告',
    REGULATORY: '监管',
    ENTERPRISE: '企业',
    COMPLIANCE_CASE: '合规案例',
    COMPLIANCE_OBLIGATION: '合规义务',
  }
  return typeLabels[type] || type
}

// 获取审查类型标签颜色
function getReviewTypeTagType(type: string): 'success' | 'warning' | 'danger' | 'info' | 'primary' {
  const typeColors: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
    CONTRACT: 'primary',
    DECISION: 'warning',
    SUPPLEMENTAL: 'info',
    VIOLATION: 'danger',
    INVESTIGATE_TASK: 'warning',
    INVESTIGATE_RECORD: 'info',
    INVESTIGATE_REPORT: 'success',
    RESPONSIBILITY_CORRECTION: 'warning',
    RESPONSIBILITY_DEAL: 'danger',
    CONTINUOUS_EXPERIENCE: 'success',
    CONTINUOUS_IMPROVE: 'primary',
    CONTINUOUS_REPORT: 'info',
    REGULATORY: 'warning',
    ENTERPRISE: 'primary',
    COMPLIANCE_CASE: 'success',
    COMPLIANCE_OBLIGATION: 'info',
  }
  return typeColors[type] || 'primary'
}

// 返回上一页
function goBack() {
  router.back()
}

// 编辑备案
function editFiling() {
  if (!detailData.value?.id) {
    ElMessage.error('缺少备案ID参数')
    return
  }
  router.push({
    path: '/monitor/examination/contractRecord/edit',
    query: { id: detailData.value.id },
  })
}

// 删除备案
async function deleteFiling() {
  if (!detailData.value?.id) {
    ElMessage.error('缺少备案ID参数')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条审查备案记录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 这里需要根据实际API调整删除接口
    ElMessage.success('删除成功')
    router.back()
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 导出备案
function exportFiling() {
  ElMessage.info('导出功能开发中')
}

// 页面初始化
onMounted(() => {
  getDetailData()
})
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <h1 class="mr-4 text-2xl c-[#000000] font-bold">
              {{ detailData?.name || '审查备案详情' }}
            </h1>
            <el-tag :type="getReviewTypeTagType(detailData?.reviewType)" size="large">
              {{ getReviewTypeLabel(detailData?.reviewType) }}
            </el-tag>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'contractRecord/detail/edit'" v-debounce="2000" plain class="rounded-button" @click="editFiling">
              <el-icon class="mr-2">
                <ElIconEdit />
              </el-icon>编辑
            </el-button>
            <el-button v-auth="'contractRecord/detail/delete'" v-debounce="2000" plain class="rounded-button" type="danger" @click="deleteFiling">
              <el-icon class="mr-2">
                <ElIconDocument />
              </el-icon>删除
            </el-button>
            <el-button v-auth="'contractRecord/detail/export'" v-debounce="2000" class="rounded-button whitespace-nowrap" @click="exportFiling">
              导出备案
            </el-button>
            <el-button v-auth="'contractRecord/detail/back'" v-debounce="2000" class="rounded-button whitespace-nowrap" @click="goBack">
              <el-icon class="mr-2">
                <ArrowLeft />
              </el-icon>返回
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <PageMain v-loading="loading" style="background-color: transparent;">
      <!-- 主内容区 -->
      <div class="flex">
        <!-- 页面内容 -->
        <div class="flex-1 overflow-y-auto">
          <div class="grid grid-cols-3 gap-6">
            <!-- 左侧内容区 -->
            <div class="col-span-2 space-y-6">
              <!-- 基本信息卡片 -->
              <el-card shadow="hover" class="rounded-lg">
                <template #header>
                  <div class="f-16 fw-600">
                    基本信息
                  </div>
                </template>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <p class="text-gray-500">
                      备案ID
                    </p>
                    <p class="font-medium">
                      {{ detailData?.id || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      租户ID
                    </p>
                    <p class="font-medium">
                      {{ detailData?.tenantId || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      对象ID
                    </p>
                    <p class="font-medium">
                      {{ detailData?.objectId || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      备案名称
                    </p>
                    <p class="font-medium">
                      {{ detailData?.name || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      审查类型
                    </p>
                    <el-tag :type="getReviewTypeTagType(detailData?.reviewType)" size="small">
                      {{ getReviewTypeLabel(detailData?.reviewType) }}
                    </el-tag>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      版本号
                    </p>
                    <p class="font-medium">
                      {{ detailData?.version || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      创建者
                    </p>
                    <p class="font-medium">
                      {{ detailData?.createdBy || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      创建时间
                    </p>
                    <p class="font-medium">
                      {{ detailData?.createdAt || '-' }}
                    </p>
                  </div>
                  <div>
                    <p class="text-gray-500">
                      更新者
                    </p>
                    <p class="font-medium">
                      {{ detailData?.updatedBy || '-' }}
                    </p>
                  </div>
                  <!-- <div>
                    <p class="text-gray-500">
                      更新时间
                    </p>
                    <p class="font-medium">
                      {{ detailData?.updatedAt || '-' }}
                    </p>
                  </div> -->
                </div>
              </el-card>

              <!-- 备案内容卡片 -->
              <el-card shadow="hover" class="rounded-lg">
                <template #header>
                  <div class="f-16 fw-600">
                    备案内容
                  </div>
                </template>
                <div class="space-y-6">
                  <!-- 备案详细内容 -->
                  <div>
                    <h4 class="mb-2 text-gray-600 font-medium">
                      备案详情
                    </h4>
                    <RichTextViewer
                      :content="detailData?.content || '暂无备案内容'"
                      :min-height="200"
                      :max-height="600"
                    />
                  </div>

                  <!-- 扩展信息 -->
                  <div v-if="detailData?.metadata && Object.keys(detailData.metadata).length > 0">
                    <h4 class="mb-2 text-gray-600 font-medium">
                      扩展信息
                    </h4>
                    <div class="rounded bg-gray-50 p-4">
                      <el-table :data="Object.entries(detailData.metadata).map(([key, value]) => ({ key, value }))" border class="w-full">
                        <el-table-column prop="key" label="字段名" width="200" />
                        <el-table-column prop="value" label="字段值">
                          <template #default="{ row }">
                            <span v-if="typeof row.value === 'object'">{{ JSON.stringify(row.value) }}</span>
                            <span v-else>{{ row.value }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>

                  <!-- 备案状态 -->
                  <div>
                    <h4 class="mb-2 text-gray-600 font-medium">
                      备案状态
                    </h4>
                    <div class="rounded bg-gray-50 p-4">
                      <el-tag :type="detailData?.isDeleted ? 'danger' : 'success'" size="large">
                        {{ detailData?.isDeleted ? '已删除' : '正常' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>

            <!-- 右侧信息栏 -->
            <div class="space-y-6">
              <!-- 操作记录 -->
              <el-card shadow="hover" class="rounded-lg">
                <template #header>
                  <div class="f-16 fw-600">
                    操作记录
                  </div>
                </template>
                <div class="space-y-4">
                  <div class="border-l-4 border-blue-500 pl-4">
                    <div class="flex items-center justify-between">
                      <span class="font-medium">创建备案</span>
                      <el-icon class="text-blue-500">
                        <ElIconCheck />
                      </el-icon>
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ detailData?.createdBy }} · {{ detailData?.createdAt || '-' }}
                    </div>
                  </div>

                  <div v-if="detailData?.updatedBy && detailData?.updatedAt" class="border-l-4 border-green-500 pl-4">
                    <div class="flex items-center justify-between">
                      <span class="font-medium">更新备案</span>
                      <el-icon class="text-green-500">
                        <ElIconEdit />
                      </el-icon>
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ detailData?.updatedBy }} · {{ detailData?.updatedAt || '-' }}
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 相关信息 -->
              <el-card shadow="hover" class="rounded-lg">
                <template #header>
                  <div class="f-16 fw-600">
                    相关信息
                  </div>
                </template>
                <div class="space-y-4">
                  <div>
                    <div class="mb-2 flex items-center">
                      <el-icon class="mr-2 text-green-500">
                        <ElIconStar />
                      </el-icon>
                      <span class="font-medium">版本信息</span>
                    </div>
                    <div class="text-sm text-gray-600">
                      当前版本: v{{ detailData?.version || '1' }}
                    </div>
                  </div>

                  <div>
                    <div class="mb-2 flex items-center">
                      <el-icon class="mr-2 text-orange-500">
                        <ElIconUser />
                      </el-icon>
                      <span class="font-medium">租户信息</span>
                    </div>
                    <div class="text-sm text-gray-600">
                      租户ID: {{ detailData?.tenantId || '-' }}
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
.absolute-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.rounded-button {
  border-radius: 6px;
}

.rounded-lg {
  border-radius: 8px;
}
</style>
