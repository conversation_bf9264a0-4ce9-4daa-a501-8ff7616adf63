<script setup>
import { markRaw, nextTick, onBeforeUnmount, onMounted, ref, shallowRef } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Check,
  Document,
  List,
  Warning,
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import dashboardApi from '@/api/monitor/dashboard'

// 响应式数据
const riskChartRef = ref(null)
const taskChartRef = ref(null)
const riskLoading = ref(false)
const taskLoading = ref(false)
const activeTimeRange = ref('7d')
const taskTrendData = ref([])

// 顶部概览卡片数据
const overviewCards = shallowRef([
  { icon: markRaw(Document), value: '0', title: '调查任务数', color: '#1890ff' },
  { icon: markRaw(List), value: '0', title: '整改任务数', color: '#fa8c16' },
  { icon: markRaw(Warning), value: '0', title: '高风险任务', color: '#f5222d' },
  { icon: markRaw(Check), value: '0%', title: '完成率', color: '#52c41a' },
])

// ECharts实例
let riskChart = null
let taskChart = null

// 防抖定时器
let debounceTimer = null

// 初始化风险分析图表
function initRiskChart() {
  if (!riskChartRef.value) {
    return
  }

  riskChart = echarts.init(riskChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '风险分布',
        type: 'pie',
        radius: '50%',
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
    color: ['#f5222d', '#fa8c16', '#52c41a'],
  }

  riskChart.setOption(option)
}

// 初始化任务状态图表
function initTaskChart() {
  if (!taskChartRef.value) {
    return
  }

  taskChart = echarts.init(taskChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['新建任务', '完成任务', '超期任务'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['任务统计'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '新建任务',
        type: 'bar',
        data: [0],
        itemStyle: { color: '#1890ff' },
      },
      {
        name: '完成任务',
        type: 'bar',
        data: [0],
        itemStyle: { color: '#52c41a' },
      },
      {
        name: '超期任务',
        type: 'bar',
        data: [0],
        itemStyle: { color: '#f5222d' },
      },
    ],
  }

  taskChart.setOption(option)
}

// 获取仪表板统计数据
async function loadDashboardData() {
  try {
    const response = await dashboardApi.queryComplianceDashboard()
    const data = response

    // 更新概览卡片数据
    overviewCards.value[0].value = data.investigateTasks || '0'
    overviewCards.value[1].value = data.improvementTasks || '0'
    overviewCards.value[2].value = data.highRiskTasks || '0'
    overviewCards.value[3].value = data.completionRate || '0%'
  }
  catch (error) {
    console.error('加载仪表板数据失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }
}

// 获取风险分布数据
async function getRiskData() {
  try {
    riskLoading.value = true
    const response = await dashboardApi.queryRiskDistribution()
    const data = response

    // 转换数据格式
    const riskData = data.map(item => ({
      name: item.riskLevel,
      value: item.count || 0,
    }))

    // 更新图表
    if (riskChart) {
      riskChart.setOption({
        series: [{
          data: riskData,
        }],
      })
    }
  }
  catch (error) {
    console.error('加载风险分布数据失败:', error)
    // 错误提示已在响应拦截器中统一处理

    // 使用默认数据
    if (riskChart) {
      riskChart.setOption({
        series: [{
          data: [
            { name: '高风险', value: 15 },
            { name: '中风险', value: 35 },
            { name: '低风险', value: 50 },
          ],
        }],
      })
    }
  }
  finally {
    riskLoading.value = false
  }
}

// 根据时间范围更新任务状态数据
function updateTaskStatusByTimeRange(range) {
  if (taskTrendData.value.length === 0) {
    return
  }

  // 时间范围到数组索引的映射
  const timeRangeMap = {
    '7d': 0, // 最近7天
    '30d': 1, // 最近30天
    '6m': 2, // 最近6个月
    '1y': 3, // 最近1年
  }

  const index = timeRangeMap[range]
  if (index !== undefined && taskTrendData.value[index]) {
    const data = taskTrendData.value[index]

    // 更新图表数据
    if (taskChart) {
      taskChart.setOption({
        series: [
          { data: [data.newCount || 0] },
          { data: [data.completedCount || 0] },
          { data: [data.overdueCount || 0] },
        ],
      })
    }
  }
}

// 获取任务状态趋势数据
async function getTaskStatusData() {
  try {
    taskLoading.value = true
    const response = await dashboardApi.queryTaskStatusTrend()
    const data = response

    // 保存原始数据
    taskTrendData.value = data

    // 根据当前选中的时间范围更新图表
    updateTaskStatusByTimeRange(activeTimeRange.value)
  }
  catch (error) {
    console.error('加载任务状态数据失败:', error)
    // 错误提示已在响应拦截器中统一处理

    // 使用默认数据
    if (taskChart) {
      taskChart.setOption({
        series: [
          { data: [25] },
          { data: [68] },
          { data: [12] },
        ],
      })
    }
  }
  finally {
    taskLoading.value = false
  }
}

// 时间范围切换
function changeTimeRange(range) {
  // 清除之前的防抖定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 设置防抖，延迟500ms执行图表更新
  debounceTimer = setTimeout(() => {
    updateTaskStatusByTimeRange(range)
  }, 500)
}

// 窗口大小变化时重新调整图表
function handleResize() {
  if (riskChart) {
    riskChart.resize()
  }
  if (taskChart) {
    taskChart.resize()
  }
}

// 组件挂载
onMounted(async () => {
  await nextTick()

  // 初始化图表
  initRiskChart()
  initTaskChart()

  // 加载数据
  loadDashboardData()
  getRiskData()
  getTaskStatusData()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
function cleanup() {
  if (riskChart) {
    riskChart.dispose()
    riskChart = null
  }
  if (taskChart) {
    taskChart.dispose()
    taskChart = null
  }
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  window.removeEventListener('resize', handleResize)
}

// 在组件卸载时清理资源
onBeforeUnmount(() => {
  cleanup()
})
</script>

<template>
  <div class="compliance-dashboard">
    <!-- 顶部概览卡片区 -->
    <div class="overview-cards">
      <el-card
        v-for="(card, index) in overviewCards"
        :key="index"
        class="overview-card"
        :style="{ borderLeft: `4px solid ${card.color}` }"
      >
        <div class="card-content">
          <div class="card-info">
            <div class="card-value">{{ card.value }}</div>
            <div class="card-title">{{ card.title }}</div>
          </div>
          <div class="card-icon" :style="{ backgroundColor: card.color }">
            <el-icon :size="24" color="#fff">
              <component :is="card.icon" />
            </el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 风险分析 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <h3>风险分析</h3>
          </div>
        </template>
        <div
          ref="riskChartRef"
          v-loading="riskLoading"
          class="chart"
        />
      </el-card>

      <!-- 任务状态统计 -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <h3>任务状态统计</h3>
            <div class="time-tabs">
              <el-radio-group v-model="activeTimeRange" @change="changeTimeRange">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="6m">6个月</el-radio-button>
                <el-radio-button label="1y">1年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div
          ref="taskChartRef"
          v-loading="taskLoading"
          class="chart"
        />
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.compliance-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
  margin-bottom: 8px;
}

.card-title {
  font-size: 14px;
  color: #666;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20px;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart {
  width: 100%;
  height: 400px;
}

.time-tabs {
  display: flex;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compliance-dashboard {
    padding: 10px;
  }

  .overview-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }

  .charts-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .chart {
    height: 300px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
