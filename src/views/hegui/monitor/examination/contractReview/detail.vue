<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Clock,
  Comment,
  Document,
  Download,
  Files,
  FullScreen,
  Printer,
  Promotion,
  User,
  VideoPlay,
  ZoomIn,
  ZoomOut,
} from '@element-plus/icons-vue'
import contractApi from '@/api/review/contract'
import dictApi from '@/api/modules/system/dict'
import DocumentUpload from '@/components/DocumentUpload/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import InitiateDialog from '@/components/initiate/index.vue'
import ReviewResultDialog from '@/components/ReviewResultDialog/index.vue'
import LoadingAnimation from '@/components/LoadingAnimation/index.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const contractDetail = ref<any>({})
const payWayOptions = ref<Array<{ label: string, value: string }>>([])
const solveWayOptions = ref<Array<{ label: string, value: string }>>([])

// 审查发起弹窗相关数据
const showInitiateDialog = ref(false)

// 审查结果弹窗相关数据
const showReviewResultDialog = ref(false)

// 审查记录弹窗相关数据
const showRecordsDialog = ref(false)
const reviewRecords = ref<any[]>([])
const recordsLoading = ref(false)

// 审查记录详情弹窗相关数据
const showRecordDetailDialog = ref(false)
const currentRecord = ref<any>(null)

const activeTab = ref('detail')

const contractElements = ref([
  {
    name: '合同主体',
    content: '甲方：XX科技有限公司\n乙方：阿里云计算有限公司',
    compliance: '合规',
    remark: '双方资质齐全',
  },
  {
    name: '合同金额',
    content: '人民币1,200,000元（含税）',
    compliance: '存疑',
    remark: '需确认预算审批',
  },
  {
    name: '履行期限',
    content: '2023年5月1日至2024年4月30日',
    compliance: '合规',
    remark: '符合公司年度采购周期',
  },
  {
    name: '违约责任',
    content: '服务不达标按日扣减0.1%合同金额，最高不超过合同总额10%',
    compliance: '不合规',
    remark: '违约金比例低于公司标准',
  },
  {
    name: '数据安全',
    content: '乙方需符合等保三级要求，数据不出境',
    compliance: '合规',
    remark: '已确认阿里云等保三级资质',
  },
])

// 获取详情数据
async function getDetailData() {
  if (!route.query.id) {
    ElMessage.error('缺少ID参数')
    router.push('/monitor/examination/contractReview')
    return
  }

  try {
    loading.value = true
    const response = await contractApi.contractReview({}, { id: route.query.id }, 'info')

    if (response) {
      contractDetail.value = response

      // 更新合同要素数据
      updateContractElements(response)
    }
  }
  catch (error) {
    console.error('获取详情失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }
  finally {
    loading.value = false
  }
}

// 更新合同要素
function updateContractElements(data: any) {
  const elements = []

  if (data.contractMessage) {
    const msg = data.contractMessage

    // 合同主体
    if (msg.firstParty || msg.secondParty) {
      elements.push({
        name: '合同主体',
        content: `甲方：${msg.firstParty || ''}\n乙方：${msg.secondParty || ''}${msg.thirdParty ? `\n丙方：${msg.thirdParty}` : ''}`,
        compliance: '合规',
        remark: '双方资质齐全',
      })
    }

    // 合同金额
    if (msg.money) {
      elements.push({
        name: '合同金额',
        content: `人民币${msg.money.toLocaleString()}元`,
        compliance: '合规',
        remark: '金额符合预算',
      })
    }

    // 履行期限
    if (msg.performancePeriodStart && msg.performancePeriodEnd) {
      elements.push({
        name: '履行期限',
        content: `${msg.performancePeriodStart} 至 ${msg.performancePeriodEnd}`,
        compliance: '合规',
        remark: '期限合理',
      })
    }

    // 违约责任
    if (msg.defaultResponsibility) {
      elements.push({
        name: '违约责任',
        content: msg.defaultResponsibility,
        compliance: '合规',
        remark: '条款完善',
      })
    }

    // 争议解决
    if (msg.solveWay) {
      elements.push({
        name: '争议解决',
        content: msg.solveWay,
        compliance: '合规',
        remark: '解决方式明确',
      })
    }
  }

  if (elements.length > 0) {
    contractElements.value = elements
  }
}

// 返回列表
function handleBack() {
  router.back()
}

// 编辑
function _handleEdit() {
  router.push(`/monitor/examination/contractReview/addEdit?id=${route.query.id}`)
}

// 检查审核权限
async function checkAuditPermission() {
  if (!contractDetail.value.id) {
    return false
  }

  try {
    const res = await contractApi.getComplianceProcess({
      objectId: contractDetail.value.id,
      reviewType: 'CONTRACT',
    })
    // 根据接口返回的isAudit字段判断审核权限
    return res
  }
  catch (err) {
    // console.error('检查审核权限失败:', err)
    return false
  }
}

// 一键审核
async function startReview() {
  if (!contractDetail.value.id) {
    ElMessage.error('参数错误')
    return
  }

  // 根据complianceReview字段判断跳转页面
  if (contractDetail.value.complianceReview?.id === null) {
    // complianceReview为null，显示审查发起弹窗
    showInitiateDialog.value = true
  }
  else {
    // complianceReview不为null，先检查审核权限
    const hasAuditPermission = await checkAuditPermission()
    if (!hasAuditPermission) {
      ElMessage.error('暂无审核权限')
      return
    }

    if (hasAuditPermission.isSubmit) {
      // 显示审查发起弹窗
      showInitiateDialog.value = true
      return
    }
    else {
      if (!hasAuditPermission.isAudit) {
        ElMessage.error('暂无审核权限')
        return
      }
    }

    // 显示审查结果弹窗
    ElMessageBox.confirm(
      '确定要进行合规审查吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    ).then(() => {
      showReviewResultDialog.value = true
    }).catch(() => {
      // 用户点击了取消
      // 不进行智能审查操作
    })
  }
}

// 提交审查
function submitReview() {
  console.log('提交审查')
}

// 审查记录
function reviewRecord() {
  if (!contractDetail.value.id) {
    ElMessage.error('系统错误')
    return
  }
  showRecordsDialog.value = true
  getReviewRecords()
}

// 获取审查记录
async function getReviewRecords() {
  try {
    recordsLoading.value = true
    const response = await contractApi.queryAllReviews(contractDetail.value.id!)
    reviewRecords.value = response.data || response || []
  }
  catch (error) {
    console.error('获取审查记录失败:', error)
    ElMessage.error('获取审查记录失败')
  }
  finally {
    recordsLoading.value = false
  }
}

// 获取审查状态标签类型
function getStatusType(status: string): 'success' | 'warning' | 'danger' | 'info' | 'primary' {
  const statusMap: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
    PENDING: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    REJECTED: 'danger',
  }
  return statusMap[status] || 'info'
}

// 关闭审查记录弹窗
function handleRecordsDialogClose() {
  showRecordsDialog.value = false
  reviewRecords.value = []
}

// 查看审查记录详情
function handleViewRecordDetail(record: any) {
  currentRecord.value = record
  showRecordDetailDialog.value = true
}

// 获取审查意见标签类型
function getOpinionType(opinion: string): 'success' | 'warning' | 'danger' | 'info' {
  const opinionMap: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    通过: 'success',
    合规: 'success',
    不通过: 'danger',
    不合规: 'danger',
    需修改: 'warning',
    存疑: 'warning',
  }
  return opinionMap[opinion] || 'info'
}

// 格式化审查记录内容
function formatRecordContent(content: string): string {
  if (!content) { return '' }

  // 将换行符转换为HTML换行
  let formattedContent = content.replace(/\n/g, '<br>')

  // 处理标题格式（### 标题）
  formattedContent = formattedContent.replace(/###\s*([^<]+)/g, '<h3 class="text-lg font-semibold text-gray-800 mt-6 mb-3">$1</h3>')

  // 处理二级标题格式（#### 标题）
  formattedContent = formattedContent.replace(/####\s*([^<]+)/g, '<h4 class="text-base font-semibold text-gray-700 mt-4 mb-2">$1</h4>')

  // 处理粗体格式（**文本**）
  formattedContent = formattedContent.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-semibold text-gray-800">$1</strong>')

  // 处理列表项（- 项目）
  formattedContent = formattedContent.replace(/^-\s*([^<]+)/gm, '<li class="ml-4 mb-1">• $1</li>')

  // 处理数字列表（1. 项目）
  formattedContent = formattedContent.replace(/^\d+\.\s*([^<]+)/gm, '<li class="ml-4 mb-1 list-decimal">$1</li>')

  // 处理分割线（---）
  formattedContent = formattedContent.replace(/---/g, '<hr class="my-4 border-gray-200">')

  // 处理段落
  const paragraphs = formattedContent.split('<br><br>')
  formattedContent = paragraphs.map((p) => {
    if (p.trim() && !p.includes('<h') && !p.includes('<li') && !p.includes('<hr')) {
      return `<p class="mb-3 text-gray-700 leading-relaxed">${p}</p>`
    }
    return p
  }).join('')

  return formattedContent
}

// 打印审查记录
function handlePrintRecord() {
  if (!currentRecord.value) { return }

  const printContent = `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="text-align: center; margin-bottom: 30px;">审查记录详情</h2>
      <div style="margin-bottom: 20px;">
        <p><strong>审查人：</strong>${currentRecord.value.createdBy}</p>
        <p><strong>创建时间：</strong>${currentRecord.value.createdAt}</p>
        <p><strong>审查意见：</strong>${currentRecord.value.opinion}</p>
      </div>
      <div style="border-top: 1px solid #ccc; padding-top: 20px;">
        <h3>审查内容：</h3>
        <div style="margin-top: 10px; line-height: 1.6;">
          ${formatRecordContent(currentRecord.value.content)}
        </div>
      </div>
    </div>
  `

  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(`
      <html>
        <head>
          <title>审查记录详情</title>
          <style>
            body { margin: 0; padding: 0; }
            @media print {
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          ${printContent}
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

// 处理审查发起弹窗成功事件
function handleInitiateSuccess() {
  // 刷新页面数据
  getDetailData()
}

// 导出报告
function exportReport() {
  console.log('导出报告')
}

// 打印
function printReport() {
  console.log('打印')
}

// 返回
function goBack() {
  router.push('/monitor/examination/contractReview')
}

// 状态映射
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    MODIFY: '需修改',
    PENDING: '待审查',
    PUBLISHED: '已发布',
    REVIEWING: '审查中',
    REVOKE: '已撤回',
  }
  return statusMap[status] || status
}

// 级别映射
function getLevelText(level: string) {
  const levelMap: Record<string, string> = {
    GENERAL: '一般',
    IMPORTANT: '重要',
    CRITICAL: '关键',
  }
  return levelMap[level] || level
}

// 合同类型映射
function getContractTypeText(type: string) {
  const typeMap: Record<string, string> = {
    TECHNICAL_SERVICES: '技术服务合同',
    MAJOR_DECISIONS: '重大决策',
    DATA: '数据合同',
    PROCUREMENT: '采购合同',
  }
  return typeMap[type] || type
}

// 风险类型映射
function _getRiskTypeText(type: string) {
  const typeMap: Record<string, string> = {
    LAWS: '法律风险',
    FINANCE: '财务风险',
    OPERATION: '操作风险',
    REPUTATION: '声誉风险',
    OTHER: '其他风险',
  }
  return typeMap[type] || type
}

// 获取付款方式选项
async function getPayWayOptions() {
  try {
    const response = await dictApi.dictAll(60)
    if (response) {
      payWayOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取付款方式选项失败:', error)
  }
}

// 获取争议解决方式选项
async function getSolveWayOptions() {
  try {
    const response = await dictApi.dictAll(63)
    if (response) {
      solveWayOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取争议解决方式选项失败:', error)
  }
}

// 付款方式映射
function getPayWayText(value: string) {
  if (!value) { return '-' }
  const option = payWayOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

// 争议解决方式映射
function getSolveWayText(value: string) {
  if (!value) { return '-' }
  const option = solveWayOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

// 初始化
onMounted(() => {
  getPayWayOptions()
  getSolveWayOptions()
  getDetailData()
})

const _attachments = ref([
  {
    name: '阿里云服务报价单',
    type: 'Excel',
    size: '256 KB',
    uploadTime: '2023-04-20 10:15',
  },
  {
    name: '阿里云等保三级证书',
    type: 'PDF',
    size: '1.2 MB',
    uploadTime: '2023-04-21 14:30',
  },
  {
    name: '技术方案确认函',
    type: 'Word',
    size: '512 KB',
    uploadTime: '2023-04-22 09:45',
  },
])

const reviewSteps = ref([
  {
    step: 1,
    title: '初审完成',
    time: '2023-04-26 10:30',
    person: '合规专员：李合规',
    statusClass: 'bg-secondary text-white',
    timeClass: 'text-gray-500',
  },
  {
    step: 2,
    title: '法务审核中',
    time: '预计2023-04-28完成',
    person: '法务专员：王法务',
    statusClass: 'bg-primary text-white',
    timeClass: 'text-gray-500',
  },
  {
    step: 3,
    title: '合规审核',
    time: '待开始',
    statusClass: 'bg-gray-200 text-gray-500',
    timeClass: 'text-gray-400',
  },
  {
    step: 4,
    title: '终审',
    time: '待开始',
    statusClass: 'bg-gray-200 text-gray-500',
    timeClass: 'text-gray-400',
  },
])

const relatedLaws = ref([
  {
    name: '《中华人民共和国合同法》',
    articles: '第52条、第107条',
  },
  {
    name: '《网络安全法》',
    articles: '第21条、第31条',
  },
  {
    name: '《数据安全法》',
    articles: '第27条、第30条',
  },
  {
    name: '《个人信息保护法》',
    articles: '第28条、第51条',
  },
  {
    name: '《XX公司采购管理办法》',
    articles: '第5章第3节',
  },
])

const relatedCases = ref([
  {
    name: '2022年度云服务采购合同审查',
    desc: '审查结果：有条件通过',
  },
  {
    name: '腾讯云服务合同纠纷案例',
    desc: '案例编号：AL2021-036',
  },
  {
    name: '华为云数据安全合规案例',
    desc: '参考价值：高',
  },
])

function getTagType(status: string): 'success' | 'warning' | 'danger' | 'info' {
  switch (status) {
    case '合规': return 'success'
    case '存疑': return 'warning'
    case '不合规': return 'danger'
    default: return 'info'
  }
}

// 获取状态标签类型
function getStatusTagType(status: string): 'success' | 'warning' | 'danger' | 'info' | 'primary' {
  const typeMap: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
    PENDING: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger',
    MODIFY: 'info',
    DRAFT: 'info',
  }
  return typeMap[status] || 'info'
}

// 获取级别标签类型
function getLevelTagType(level: string): 'success' | 'warning' | 'danger' | 'info' | 'primary' {
  const typeMap: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
    LOW: 'info',
    MEDIUM: 'warning',
    HIGH: 'danger',
  }
  return typeMap[level] || 'info'
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ contractDetail.name || '合同审查详情' }}
            </h1>
            <el-tag :type="getStatusTagType(contractDetail.status)" class="ml-4">
              {{ getStatusText(contractDetail.status) }}
            </el-tag>
          </div>
          <div class="flex items-center space-x-3">
            <el-button v-debounce="2000" :disabled="loading || contractDetail.status === 'PUBLISHED'" type="primary" class="!rounded-button" @click="startReview">
              <el-icon class="mr-2">
                <VideoPlay />
              </el-icon>一键审核
            </el-button>
            <el-button v-debounce="2000" :disabled="loading" plain class="!rounded-button" @click="reviewRecord">
              <el-icon class="mr-2">
                <Promotion />
              </el-icon>审查记录
            </el-button>
            <!-- <el-button :disabled="loading" plain class="!rounded-button" @click="submitReview">
                <el-icon class="mr-2">
                  <Promotion />
                </el-icon>审核记录
              </el-button> -->
            <el-button v-debounce="2000" :disabled="loading" plain class="!rounded-button" @click="handleBack">
              <el-icon class="mr-2">
                <ArrowLeft />
              </el-icon>返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="!mx-0">
          <el-col class="m-auto" :span="18">
            <el-card class="">
              <div class="grid grid-cols-2 mb-6 gap-6">
                <div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查编号</label>
                    <p class="font-medium">
                      {{ contractDetail.reviewCode }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">合同名称</label>
                    <p class="font-medium">
                      {{ contractDetail.name }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">合同类型</label>
                    <p class="font-medium">
                      {{ getContractTypeText(contractDetail.contractType) }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查级别</label>
                    <p class="font-medium">
                      <el-tag :type="getLevelTagType(contractDetail.level)">
                        {{ getLevelText(contractDetail.level) }}
                      </el-tag>
                    </p>
                  </div>
                  <div>
                    <label class="block text-sm text-gray-500">发起部门</label>
                    <p class="font-medium">
                      {{ contractDetail.departmentName }}
                    </p>
                  </div>
                </div>
                <div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">发起人</label>
                    <p class="font-medium">
                      {{ contractDetail.createdBy || '-' }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">发起时间</label>
                    <p class="font-medium">
                      {{ contractDetail.createdAt || '-' }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查人</label>
                    <p class="font-medium">
                      {{ contractDetail.auditNameBy || '-' }}
                    </p>
                  </div>
                  <div class="mb-4">
                    <label class="block text-sm text-gray-500">审查状态</label>
                    <p class="font-medium">
                      <el-tag :type="getStatusTagType(contractDetail.status)">
                        {{ getStatusText(contractDetail.status) }}
                      </el-tag>
                    </p>
                  </div>
                  <div>
                    <label class="block text-sm text-gray-500">完成时间</label>
                    <p class="font-medium">
                      {{ contractDetail.updatedAt || '-' }}
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="合同详情" name="detail">
                  <h3 class="mb-4 text-base font-bold">
                    合同信息
                  </h3>
                  <div class="grid grid-cols-2 mb-6 gap-6">
                    <div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">甲方</label>
                        <p class="font-medium">
                          {{ contractDetail.contractMessage?.firstParty || '-' }}
                        </p>
                      </div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">乙方</label>
                        <p class="font-medium">
                          {{ contractDetail.contractMessage?.secondParty || '-' }}
                        </p>
                      </div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">丙方（如有）</label>
                        <p class="font-medium">
                          {{ contractDetail.contractMessage?.thirdParty || '-' }}
                        </p>
                      </div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">合同金额</label>
                        <p class="font-medium">
                          {{ contractDetail.contractMessage?.money ? `人民币${contractDetail.contractMessage.money.toLocaleString()}元` : '-' }}
                        </p>
                      </div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">合同签订日期</label>
                        <p class="font-medium">
                          {{ contractDetail.contractMessage?.signDate || '-' }}
                        </p>
                      </div>
                    </div>
                    <div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">合同履行期限</label>
                        <p class="font-medium">
                          {{ contractDetail.contractMessage?.performancePeriodStart && contractDetail.contractMessage?.performancePeriodEnd
                            ? `${contractDetail.contractMessage.performancePeriodStart} 至 ${contractDetail.contractMessage.performancePeriodEnd}`
                            : '-' }}
                        </p>
                      </div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">付款方式</label>
                        <p class="font-medium">
                          {{ getPayWayText(contractDetail.contractMessage?.payWay) || '-' }}
                        </p>
                      </div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">争议解决方式</label>
                        <p class="font-medium">
                          {{ getSolveWayText(contractDetail.contractMessage?.solveWay) || '-' }}
                        </p>
                      </div>
                      <div class="mb-4">
                        <label class="block text-sm text-gray-500">违约责任</label>
                        <p class="font-medium">
                          {{ contractDetail.contractMessage?.defaultResponsibility || '-' }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div v-if="contractDetail.contractMessage?.content" class="mb-6">
                    <label class="mb-2 block text-sm text-gray-500">合同内容</label>
                    <div class="border rounded bg-gray-50 p-4">
                      <p class="whitespace-pre-wrap text-sm text-gray-700">
                        {{ contractDetail.contractMessage.content }}
                      </p>
                    </div>
                  </div>
                  <h3 class="mb-4 text-base font-bold">
                    合同要素
                  </h3>
                  <el-table :data="contractElements" class="mb-6">
                    <el-table-column prop="name" label="要素名称" />
                    <el-table-column prop="content" label="内容" />
                    <el-table-column prop="compliance" label="合规评估">
                      <template #default="{ row }">
                        <el-tag :type="getTagType(row.compliance)">
                          {{ row.compliance }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="备注" />
                  </el-table>

                  <h3 class="mb-4 text-base font-bold">
                    合同文档
                  </h3>
                  <div class="mb-6">
                    <DocumentUpload
                      :model-value="contractDetail.contractMessage?.documentUrl"
                      :readonly="true"
                    />
                  </div>

                  <h3 class="mb-4 text-base font-bold">
                    合同附件
                  </h3>
                  <div class="mb-6">
                    <UploadMbb
                      :model-value="contractDetail.contractAttachments || []"
                      :readonly="true"
                    />
                  </div>
                </el-tab-pane>
                <!-- <el-tab-pane label="审查记录" name="record" />
                <el-tab-pane label="法规匹配" name="law" />
                <el-tab-pane label="风险识别" name="risk" />
                <el-tab-pane label="审查意见" name="opinion" />
                <el-tab-pane label="操作历史" name="history" /> -->
              </el-tabs>
            </el-card>
          </el-col>
          <el-col v-if="false" :span="6">
            <el-card class="">
              <template #header>
                <div class="f-16 fw-600">
                  审查进度
                </div>
              </template>
              <div class="space-y-4">
                <div v-for="(step, index) in reviewSteps" :key="index" class="flex">
                  <div class="mr-3 flex flex-col items-center">
                    <div class="h-6 w-6 rounded-full" :class="step.statusClass">
                      {{ step.step }}
                    </div>
                    <div v-if="index < reviewSteps.length - 1" class="h-8 w-px bg-gray-200" />
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      {{ step.title }}
                    </p>
                    <p class="text-xs" :class="step.timeClass">
                      {{ step.time }}
                    </p>
                    <p v-if="step.person" class="mt-1 text-xs text-gray-500">
                      {{ step.person }}
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关法规
                </div>
              </template>
              <ul class="space-y-3">
                <li v-for="(law, index) in relatedLaws" :key="index">
                  <el-link type="primary">
                    {{ law.name }}
                  </el-link>
                  <p class="mt-1 text-xs text-gray-500">
                    {{ law.articles }}
                  </p>
                </li>
              </ul>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关案例
                </div>
              </template>
              <ul class="space-y-3">
                <li v-for="(caseItem, index) in relatedCases" :key="index">
                  <el-link type="primary">
                    {{ caseItem.name }}
                  </el-link>
                  <p class="mt-1 text-xs text-gray-500">
                    {{ caseItem.desc }}
                  </p>
                </li>
              </ul>
            </el-card>
            <el-card class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-2">
                <div>
                  <el-button v-debounce="2000" type="primary" class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <Comment />
                    </el-icon>添加意见
                  </el-button>
                </div>
                <div>
                  <el-button v-debounce="2000" plain class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <Clock />
                    </el-icon>查看历史版本
                  </el-button>
                </div>
                <div>
                  <el-button v-debounce="2000" plain class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <Files />
                    </el-icon>合同比对
                  </el-button>
                </div>
                <div>
                  <el-button v-debounce="2000" plain class="!rounded-button w-full">
                    <el-icon class="mr-2">
                      <User />
                    </el-icon>咨询专家
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 审查发起弹窗 -->
    <InitiateDialog
      v-model:visible="showInitiateDialog"
      :object-id="contractDetail.id"
      review-type="CONTRACT"
      :compliance-review="contractDetail.complianceReview"
      @success="handleInitiateSuccess"
    />

    <!-- 审查结果弹窗 -->
    <ReviewResultDialog
      v-model:visible="showReviewResultDialog"
      :contract-id="contractDetail.id"
      :contract-info="{
        name: contractDetail.name,
        status: contractDetail.status,
        contractType: contractDetail.contractType,
        level: contractDetail.level,
        department: contractDetail.department,
        auditBy: contractDetail.auditBy,
        createdBy: contractDetail.createdBy,
        createdAt: contractDetail.createdAt,
      }"
    />

    <!-- 审查记录弹窗 -->
    <el-dialog
      v-model="showRecordsDialog"
      title="审查记录"
      width="800px"
      :close-on-click-modal="false"
      @close="handleRecordsDialogClose"
    >
      <div class="records-content">
        <!-- 统一加载动画 -->
        <LoadingAnimation
          :visible="recordsLoading"
          text="加载审查记录..."
          size="medium"
          :fullscreen="false"
        />

        <el-table :data="reviewRecords" style="width: 100%" max-height="500px" empty-text="暂无审查记录">
          <el-table-column prop="opinion" label="审查意见" width="120">
            <template #default="{ row }">
              <div class="opinion-cell" :title="row.opinion">
                {{ row.opinion }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createdBy" label="审查人" width="100" />
          <el-table-column prop="createdAt" label="创建时间" width="160" />
          <el-table-column prop="content" label="审查记录" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center justify-between">
                <span class="mr-2 flex-1 truncate" :title="row.content">{{ row.content }}</span>
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="handleViewRecordDetail(row)"
                >
                  查看详情
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleRecordsDialogClose">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审查记录详情弹窗 -->
    <el-dialog
      v-model="showRecordDetailDialog"
      title="审查记录详情"
      width="900px"
      :close-on-click-modal="false"
      class="record-detail-dialog"
    >
      <div v-if="currentRecord" class="record-detail-content">
        <!-- 基本信息 -->
        <div class="record-header mb-6">
          <div class="mb-4">
            <h3 class="mb-3 text-lg text-gray-800 font-semibold">
              基本信息
            </h3>
            <div class="opinion-text">
              {{ currentRecord.opinion }}
            </div>
          </div>
          <div class="grid grid-cols-2 gap-4 rounded-lg bg-gray-50 p-4">
            <div>
              <span class="text-sm text-gray-600">审查人：</span>
              <span class="font-medium">{{ currentRecord.createdBy }}</span>
            </div>
            <div>
              <span class="text-sm text-gray-600">创建时间：</span>
              <span class="font-medium">{{ currentRecord.createdAt }}</span>
            </div>
          </div>
        </div>

        <!-- 审查内容 -->
        <div class="record-content">
          <h3 class="mb-4 text-lg text-gray-800 font-semibold">
            审查内容
          </h3>
          <div class="max-w-none prose">
            <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
              <div class="record-text" v-html="formatRecordContent(currentRecord.content)" />
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRecordDetailDialog = false">
            关闭
          </el-button>
          <!-- <el-button type="primary" @click="handlePrintRecord">打印</el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
 @use "@/styles/toolsCss";
  [type="number"]::-webkit-inner-spin-button,
  [type="number"]::-webkit-outer-spin-button {
    margin: 0;
    appearance: none;
  }

  :deep(.el-card) {
    border-radius: 4px;
  }

  :deep(.el-tabs__item) {
    height: 48px;
    padding: 0 16px;
    line-height: 48px;
  }

  :deep(.el-table) {
    font-size: 14px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f9fafb;
  }

  :deep(.el-table .cell) {
    white-space: pre-line;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }

  .records-content {
    min-height: 300px;
  }

  .opinion-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }

  .opinion-text {
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.5;
    color: #374151;
    font-weight: 500;
    padding: 8px 12px;
    background-color: #f3f4f6;
    border-radius: 6px;
    border-left: 4px solid #3b82f6;
  }

  .dialog-footer {
    text-align: right;
  }

  /* 审查记录详情弹窗样式 */
  :deep(.record-detail-dialog) {
    .el-dialog__body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .record-detail-content {
    .record-text {
      line-height: 1.8;
      color: #374151;

      h3 {
        color: #1f2937;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 8px;
        margin-top: 24px;
        margin-bottom: 16px;
      }

      h4 {
        color: #374151;
        margin-top: 20px;
        margin-bottom: 12px;
      }

      p {
        margin-bottom: 12px;
        text-align: justify;
      }

      li {
        margin-bottom: 8px;
        color: #4b5563;
      }

      strong {
        color: #1f2937;
      }

      hr {
        margin: 20px 0;
        border: none;
        border-top: 1px solid #e5e7eb;
      }
    }
  }

  .records-content {
    position: relative;
    min-height: 300px;
  }
</style>
