<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import riskModelApi from '@/api/riskModel'

// 类型定义
interface RiskRule {
  id: number | null
  code: string
  name: string
  ruleType: string
  params: string
  description: string
  score: number
  categoryId: number
}

const router = useRouter()
const route = useRoute()

// 风险模型详情数据
const modelDetail = ref<any>({})
const loading = ref(false)
const modelId = ref<string | null>(null)

// 规则管理状态
const showRuleDialog = ref(false)
const showRuleFormDialog = ref(false)
const currentCategory = ref<any>(null)
const currentCategoryIndex = ref<number>(-1)
const editingRule = ref<any>(null)

// 规则列表数据
const ruleList = ref<any[]>([])
const ruleListLoading = ref(false)
const ruleListPagination = ref({
  page: 0,
  size: 10,
  total: 0,
})

// 规则表单数据
const ruleForm = reactive<RiskRule>({
  id: null,
  code: '',
  name: '',
  ruleType: 'THRESHOLD',
  params: '',
  description: '',
  score: 0,
  categoryId: 0,
})

// 规则类型选项
const ruleTypeOptions = [
  { label: '阈值规则', value: 'THRESHOLD' },
  { label: '模式规则', value: 'PATTERN' },
  { label: '脚本规则', value: 'SCRIPT' },
]

// 获取风险模型详情
async function getModelDetail() {
  if (!modelId.value) {
    return
  }

  try {
    loading.value = true
    const response = await riskModelApi.getRiskModelDetail(Number(modelId.value))
    if (response) {
      modelDetail.value = response
    }
  }
  catch (error) {
    console.error('获取风险模型详情失败:', error)
    ElMessage.error('获取风险模型详情失败')
  }
  finally {
    loading.value = false
  }
}

// 返回列表
function goBack() {
  router.back()
}

// 获取状态标签类型
function getStatusType(isDefault: boolean) {
  return isDefault ? 'success' : 'info'
}

// 获取状态文本
function getStatusText(isDefault: boolean) {
  return isDefault ? '默认模型' : '普通模型'
}

// 获取风险等级标签类型
function getRiskLevelType(level: string) {
  switch (level) {
    case 'HIGH':
      return 'danger'
    case 'MEDIUM':
      return 'warning'
    case 'LOW':
      return 'success'
    default:
      return 'info'
  }
}

// 获取风险等级文本
function getRiskLevelText(level: string) {
  switch (level) {
    case 'HIGH':
      return '高风险'
    case 'MEDIUM':
      return '中风险'
    case 'LOW':
      return '低风险'
    default:
      return '未知'
  }
}

// 获取规则类型文本
function getRuleTypeText(ruleType: string) {
  switch (ruleType) {
    case 'THRESHOLD':
      return '阈值规则'
    case 'PATTERN':
      return '模式规则'
    case 'SCRIPT':
      return '脚本规则'
    default:
      return '未知类型'
  }
}

// 规则管理函数
// 打开规则管理弹窗
async function openRuleDialog(category: any, categoryIndex: number) {
  currentCategory.value = category
  currentCategoryIndex.value = categoryIndex
  showRuleDialog.value = true
  editingRule.value = null

  // 重置分页
  ruleListPagination.value.page = 0

  // 获取规则列表
  await getRuleList()
}

// 关闭规则管理弹窗
function closeRuleDialog() {
  showRuleDialog.value = false
  currentCategory.value = null
  currentCategoryIndex.value = -1
  editingRule.value = null
  ruleList.value = []
  resetRuleForm()
}

// 获取规则列表
async function getRuleList() {
  if (!currentCategory.value) {
    return
  }

  try {
    ruleListLoading.value = true
    const params = {
      page: ruleListPagination.value.page,
      size: ruleListPagination.value.size,
      riskCategoryId: currentCategory.value.id,
    }

    const response = await riskModelApi.getAllRiskRules(params)
    if (response) {
      ruleList.value = response.content || []
      ruleListPagination.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取规则列表失败:', error)
    ElMessage.error('获取规则列表失败')
  }
  finally {
    ruleListLoading.value = false
  }
}

// 分页变化
function handlePageChange(page: number) {
  ruleListPagination.value.page = page
  getRuleList()
}

function handleSizeChange(size: number) {
  ruleListPagination.value.size = size
  ruleListPagination.value.page = 0
  getRuleList()
}

// 开始添加规则
function startAddRule() {
  editingRule.value = null
  showRuleFormDialog.value = true
  resetRuleForm()
  if (currentCategory.value) {
    ruleForm.categoryId = currentCategory.value.id
  }
}

// 开始编辑规则
function startEditRule(rule: any) {
  editingRule.value = rule
  showRuleFormDialog.value = true
  Object.assign(ruleForm, {
    id: rule.id,
    code: rule.code,
    name: rule.name,
    ruleType: rule.ruleType,
    params: rule.params,
    description: rule.description,
    score: rule.score,
    categoryId: rule.categoryId,
  })
}

// 关闭规则表单弹窗
function closeRuleFormDialog() {
  showRuleFormDialog.value = false
  editingRule.value = null
  resetRuleForm()
}

// 重置规则表单
function resetRuleForm() {
  Object.assign(ruleForm, {
    id: null,
    code: '',
    name: '',
    ruleType: 'THRESHOLD',
    params: '',
    description: '',
    score: 0,
    categoryId: currentCategory.value?.id || 0,
  })
}

// 保存规则
async function saveRule() {
  try {
    if (!ruleForm.name.trim()) {
      ElMessage.warning('请输入规则名称')
      return
    }

    if (!currentCategory.value) {
      return
    }

    const category = currentCategory.value

    // 确保分类有规则数组
    if (!category.riskRules) {
      category.riskRules = []
    }

    const ruleData = { ...ruleForm }

    if (editingRule.value && ruleForm.id) {
      // 更新现有规则
      const ruleIndex = category.riskRules.findIndex((rule: any) => rule.id === ruleForm.id)
      if (ruleIndex !== -1) {
        category.riskRules[ruleIndex] = ruleData
      }
    }
    else {
      // 添加新规则 - 不设置ID，让服务端生成
      delete ruleData.id // 确保不传入ID
      category.riskRules.push(ruleData)
    }

    // 使用 updateRiskCategory 接口更新整个分类（包含规则）
    const categoryData = {
      id: category.id,
      name: category.name,
      level: category.level,
      expression: category.expression,
      description: category.description,
      riskRules: category.riskRules,
    }

    await riskModelApi.updateRiskCategory(categoryData)
    ElMessage.success(editingRule.value ? '规则更新成功' : '规则添加成功')

    // 关闭表单弹窗
    showRuleFormDialog.value = false
    editingRule.value = null
    resetRuleForm()

    // 重新获取规则列表
    await getRuleList()

    // 重新获取模型详情以更新主页面
    await getModelDetail()

    // 更新弹窗中的当前分类数据
    if (currentCategoryIndex.value >= 0) {
      currentCategory.value = modelDetail.value.categories[currentCategoryIndex.value]
    }
  }
  catch (error) {
    console.error('保存规则失败:', error)
    ElMessage.error('保存规则失败')
  }
}

// 删除规则
async function deleteRule(rule: any) {
  try {
    // 直接调用删除规则接口
    await riskModelApi.deleteRiskRule(rule.id)
    ElMessage.success('规则删除成功')

    // 重新获取规则列表
    await getRuleList()

    // 重新获取模型详情以更新主页面
    await getModelDetail()

    // 更新弹窗中的当前分类数据
    if (currentCategoryIndex.value >= 0) {
      currentCategory.value = modelDetail.value.categories[currentCategoryIndex.value]
    }
  }
  catch (error) {
    console.error('删除规则失败:', error)
    ElMessage.error('删除规则失败')
  }
}

// 取消规则编辑
function cancelRuleEdit() {
  showRuleFormDialog.value = false
  editingRule.value = null
  resetRuleForm()
}

// 组件挂载时获取数据
onMounted(() => {
  const id = route.params.id || route.query.id
  if (id) {
    modelId.value = typeof id === 'string' ? id : id[0]
    getModelDetail()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between" />
          <div class="flex space-x-3">
            <el-button v-debounce="2000" class="!rounded-button whitespace-nowrap" @click="goBack">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-card class="!mx-0" shadow="hover">
          <div v-loading="loading" class="flex items-start justify-between">
            <div>
              <h2 class="mb-2 text-2xl text-gray-900 font-bold">
                {{ modelDetail.name || '风险模型详情' }}
              </h2>
              <div class="mb-4 flex items-center space-x-3">
                <el-tag :type="getStatusType(modelDetail.isDefault)" size="small">
                  {{ getStatusText(modelDetail.isDefault) }}
                </el-tag>
                <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">版本 {{ modelDetail.version || 1 }}</span>
              </div>
            </div>
          </div>
        </el-card>
        <el-row :gutter="20" class="mt-20">
          <el-col :span="18">
            <el-card shadow="hover">
              <div class="mb-6">
                <h3 class="mb-2 text-sm text-gray-500 font-medium">
                  模型描述
                </h3>
                <div class="rounded bg-gray-50 p-4">
                  <p class="text-gray-700">
                    {{ modelDetail.description || '暂无描述' }}
                  </p>
                </div>
              </div>

              <!-- 风险类别信息 -->
              <div v-if="modelDetail.categories && modelDetail.categories.length > 0" class="mb-6">
                <h3 class="mb-4 text-lg text-gray-900 font-medium">
                  风险类别 ({{ modelDetail.categories.length }}个)
                </h3>
                <div class="space-y-4">
                  <div v-for="category in modelDetail.categories" :key="category.id" class="border rounded bg-white p-4">
                    <div class="mb-3 flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <h4 class="text-gray-900 font-medium">
                          {{ category.name }}
                        </h4>
                        <el-tag :type="getRiskLevelType(category.level)" size="small">
                          {{ getRiskLevelText(category.level) }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="mb-3">
                      <p class="text-sm text-gray-600">
                        {{ category.description || '暂无描述' }}
                      </p>
                    </div>
                    <div class="mb-3">
                      <p class="text-sm text-gray-500">
                        <strong>入组表达式：</strong>{{ category.expression || '-' }}
                      </p>
                    </div>

                    <!-- 风险规则管理 -->
                    <div class="mt-4">
                      <div class="flex items-center justify-between">
                        <h5 class="text-sm text-gray-700 font-medium">
                          风险规则管理
                        </h5>
                        <el-button
                          type="primary"
                          size="small"
                          @click="openRuleDialog(category, modelDetail.categories.indexOf(category))"
                        >
                          👁️ 查看规则
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <div v-loading="loading" class="space-y-6">
              <!-- 基本信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    基本信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">模型名称：</span>
                    <span class="font-medium">{{ modelDetail.name || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">模型状态：</span>
                    <el-tag :type="getStatusType(modelDetail.isDefault)" size="small">
                      {{ getStatusText(modelDetail.isDefault) }}
                    </el-tag>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">模型版本：</span>
                    <span class="font-medium">{{ modelDetail.version || 1 }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 生效时间 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    生效时间
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">开始时间：</span>
                    <span class="text-sm font-medium">{{ (modelDetail.effectiveFrom) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">结束时间：</span>
                    <span class="text-sm font-medium">{{ (modelDetail.effectiveTo) }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 创建信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    创建信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">创建人：</span>
                    <span class="font-medium">{{ modelDetail.createdBy || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">创建时间：</span>
                    <span class="text-sm font-medium">{{ modelDetail.createdAt }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">最后修改：</span>
                    <span class="text-sm font-medium">{{ modelDetail.updatedAt }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">修改人：</span>
                    <span class="font-medium">{{ modelDetail.updatedBy || '-' }}</span>
                  </div>
                </div>
              </el-card>

              <!-- 统计信息 -->
              <el-card shadow="hover">
                <template #header>
                  <div class="font-bold">
                    统计信息
                  </div>
                </template>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-gray-600">风险类别数：</span>
                    <span class="text-blue-600 font-medium">{{ modelDetail.categories?.length || 0 }}个</span>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 规则管理弹窗 -->
    <el-dialog
      v-model="showRuleDialog"
      :title="`${currentCategory?.name || ''} - 规则管理`"
      width="90%"
      :before-close="closeRuleDialog"
    >
      <div v-if="currentCategory" class="space-y-4">
        <!-- 弹窗头部信息 -->
        <div class="mb-4 flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <h4 class="text-lg text-gray-900 font-medium">
              {{ currentCategory.name }}
            </h4>
            <el-tag :type="getRiskLevelType(currentCategory.level)" size="small">
              {{ getRiskLevelText(currentCategory.level) }}
            </el-tag>
            <span class="text-sm text-gray-500">共 {{ ruleListPagination.total }} 条规则</span>
          </div>
          <el-button type="primary" @click="startAddRule">
            ➕ 添加规则
          </el-button>
        </div>

        <!-- 规则表格 -->
        <el-table
          v-loading="ruleListLoading"
          :data="ruleList"
          stripe
          style="width: 100%"
          empty-text="暂无规则数据"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="code" label="规则代码" width="120" />
          <el-table-column prop="name" label="规则名称" min-width="150" />
          <el-table-column prop="ruleType" label="规则类型" width="120">
            <template #default="{ row }">
              <el-tag size="small">
                {{ getRuleTypeText(row.ruleType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="评分" width="80">
            <template #default="{ row }">
              <span class="text-orange-600 font-medium">{{ row.score || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="params" label="参数" min-width="120" show-overflow-tooltip />
          <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="startEditRule(row)"
              >
                编辑
              </el-button>
              <el-popconfirm
                :title="`确定要删除规则「${row.name}」吗？`"
                confirm-button-text="确定删除"
                cancel-button-text="取消"
                confirm-button-type="danger"
                icon="el-icon-warning"
                icon-color="#f56c6c"
                width="300"
                @confirm="deleteRule(row)"
              >
                <template #reference>
                  <el-button type="danger" size="small">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="mt-4 flex justify-center">
          <el-pagination
            v-model:current-page="ruleListPagination.page"
            v-model:page-size="ruleListPagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="ruleListPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 规则表单弹窗 -->
    <el-dialog
      v-model="showRuleFormDialog"
      :title="editingRule ? '编辑规则' : '添加规则'"
      width="60%"
      :before-close="cancelRuleEdit"
    >
      <el-form :model="ruleForm" label-width="100px" size="default">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则代码" required>
              <el-input v-model="ruleForm.code" placeholder="请输入规则代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则名称" required>
              <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则类型" required>
              <el-select v-model="ruleForm.ruleType" placeholder="请选择规则类型" style="width: 100%">
                <el-option
                  v-for="option in ruleTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评分">
              <el-input-number
                v-model="ruleForm.score"
                :min="0"
                :max="100"
                placeholder="请输入评分"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="规则参数">
          <el-input
            v-model="ruleForm.params"
            type="textarea"
            :rows="3"
            placeholder="请输入规则参数"
          />
        </el-form-item>
        <el-form-item label="规则描述">
          <el-input
            v-model="ruleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelRuleEdit">
            取消
          </el-button>
          <el-button type="primary" @click="saveRule">
            {{ editingRule ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
