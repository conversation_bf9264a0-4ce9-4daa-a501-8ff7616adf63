<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download as ElIconDownload,
  Plus as ElIconPlus,
  Refresh as ElIconRefresh,
  Search as ElIconSearch,
} from '@element-plus/icons-vue'
import contractRecordApi from '@/api/monitor/contractRecord'

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 筛选条件
const filter = ref({
  name: '',
  reviewType: '',
  createdBy: '',
})

// 表格数据
const tableData = ref<any[]>([])

// 分页数据
const paging = ref({
  page: 1,
  size: 10,
  total: 0,
})

// 选择项
const selectedItems = ref<any[]>([])

// 定义数据类型
interface FilingItem {
  id: number
  tenantId: number
  objectId: number
  name: string
  reviewType: string
  content: string
  metadata: string
  version: number
  createdBy: string
  createdAt: any
  updatedBy: string
  updatedAt: any
  isDeleted: boolean
}

// 获取审查类型标签
function getReviewTypeLabel(type: string): string {
  const typeLabels: Record<string, string> = {
    CONTRACT: '合同',
    DECISION: '重大决策',
    SUPPLEMENTAL: '补充审查',
    VIOLATION: '违规',
    INVESTIGATE_TASK: '调查任务',
    INVESTIGATE_RECORD: '调查记录',
    INVESTIGATE_REPORT: '调查报告',
    RESPONSIBILITY_CORRECTION: '责任纠正',
    RESPONSIBILITY_DEAL: '责任处理',
    CONTINUOUS_EXPERIENCE: '持续经验',
    CONTINUOUS_IMPROVE: '持续改进',
    CONTINUOUS_REPORT: '持续报告',
    REGULATORY: '监管',
    ENTERPRISE: '企业',
    COMPLIANCE_CASE: '合规案例',
    COMPLIANCE_OBLIGATION: '合规义务',
  }
  return typeLabels[type] || type
}

// 获取审查类型标签颜色
function getReviewTypeTagType(type: string): 'success' | 'warning' | 'danger' | 'info' | 'primary' {
  const typeColors: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
    CONTRACT: 'primary',
    DECISION: 'warning',
    SUPPLEMENTAL: 'info',
    VIOLATION: 'danger',
    INVESTIGATE_TASK: 'warning',
    INVESTIGATE_RECORD: 'info',
    INVESTIGATE_REPORT: 'success',
    RESPONSIBILITY_CORRECTION: 'warning',
    RESPONSIBILITY_DEAL: 'danger',
    CONTINUOUS_EXPERIENCE: 'success',
    CONTINUOUS_IMPROVE: 'primary',
    CONTINUOUS_REPORT: 'info',
    REGULATORY: 'warning',
    ENTERPRISE: 'primary',
    COMPLIANCE_CASE: 'success',
    COMPLIANCE_OBLIGATION: 'info',
  }
  return typeColors[type] || 'primary'
}

// 表格选择变化处理
function handleSelectionChange(val: any[]) {
  selectedItems.value = val
}

// 重置筛选条件
function resetFilter() {
  filter.value = {
    name: '',
    reviewType: '',
    createdBy: '',
  }
}

// 查询按钮处理
function handleQuery() {
  paging.value.page = 1
  getFilingsList()
}

// 重置按钮处理
function handleReset() {
  resetFilter()
  paging.value.page = 1
  getFilingsList()
}

// 获取审查备案列表
async function getFilingsList() {
  try {
    loading.value = true
    const params = {
      page: paging.value.page - 1, // 后端从0开始
      size: paging.value.size,
      ...filter.value,
    }

    const response = await contractRecordApi.getFilingsList(params)
    if (response) {
      tableData.value = response.content || []
      paging.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 分页变化处理
function pagChange(page: number) {
  paging.value.page = page
  getFilingsList()
}

// 查看详情
function viewDetail(row: FilingItem) {
  router.push({
    path: '/monitor/examination/contractRecord/detail',
    query: { id: row.id },
  })
}

// 编辑
function editItem(row: FilingItem) {
  router.push({
    path: '/monitor/examination/contractRecord/edit',
    query: { id: row.id },
  })
}

// 删除
async function deleteItem(row: FilingItem) {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条审查备案记录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 这里需要根据实际API调整删除接口
    ElMessage.success('删除成功')
    getFilingsList()
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 导出
function exportItem(_row: FilingItem) {
  ElMessage.info('导出功能开发中')
}

// 新增
function addItem() {
  router.push('/monitor/examination/contractRecord/add')
}

// 页面初始化
onMounted(() => {
  getFilingsList()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              合同备案
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'contractRecord/index/add'" v-debounce="2000" type="primary" @click="addItem">
              <el-icon class="mr-2">
                <ElIconPlus />
              </el-icon>
              新增备案
            </el-button>
            <el-button v-auth="'contractRecord/index/export'" v-debounce="2000">
              <el-icon class="mr-2">
                <ElIconDownload />
              </el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-card shadow="hover" class="">
          <div class="p-6">
            <!-- 查询表单 -->
            <!-- <div class="mb-5 flex justify-end">
              <el-button v-debounce="1000" type="primary" @click="handleQuery">
                <el-icon class="mr-1">
                  <ElIconSearch />
                </el-icon>
                查询
              </el-button>
              <el-button v-debounce="1000" @click="handleReset">
                <el-icon class="mr-1">
                  <ElIconRefresh />
                </el-icon>
                重置
              </el-button>
            </div> -->

            <!-- 表格 -->
            <el-table v-loading="loading" :data="tableData" style="width: 100%;" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="name" label="备案名称" width="200" />
              <el-table-column prop="reviewType" label="审查类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getReviewTypeTagType(row.reviewType)" size="small">
                    {{ getReviewTypeLabel(row.reviewType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="content" label="备案内容" width="200" show-overflow-tooltip />
              <el-table-column prop="version" label="版本" width="80" />
              <el-table-column prop="createdBy" label="创建者" width="120" />
              <el-table-column prop="createdAt" label="创建时间" width="150" />
              <el-table-column prop="updatedBy" label="更新者" width="120" />
              <el-table-column prop="updatedAt" label="更新时间" width="150" />
              <el-table-column label="操作" width="80" fixed="right">
                <template #default="{ row }">
                  <el-button v-auth="'contractRecord/index/view'" v-debounce="2000" class="!ml-0" type="primary" plain size="small" @click="viewDetail(row)">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <page-compon
              :page="paging.page" :size="paging.size" :total="paging.total" style="margin-top: 16px;"
              @pag-change="pagChange"
            />
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
.absolute-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
