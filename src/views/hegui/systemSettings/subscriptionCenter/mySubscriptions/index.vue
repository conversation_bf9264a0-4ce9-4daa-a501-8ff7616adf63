<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import {
  ArrowLeft,
  ArrowRight,
  Bell,
  Box,
  CircleCheck,
  Close,
  Document,
  InfoFilled,
  Lock,
  Medal,
  Refresh,
  Warning,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import subscriptionsApi from '@/api/subscriptions/index'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
// 加载状态
const loading = ref(false)

// 企业高级版数据 - 只取最新一条
const enterpriseData = ref<any>(null)

// 许可信息数据 - 只取最新一条
const licenseData = ref<any>(null)

// 账单状态数据 - 只取最新一条
const billData = ref<any>(null)

// 已启用功能模块数据 - 显示4条
const enabledModules = ref<any[]>([])
const modulesLoading = ref(false)
const currentModulePage = ref(0) // 当前页码
const totalModulePages = ref(0) // 总页数

// 配额使用情况数据
const quotaData = ref<any>(null)
const quotaLoading = ref(false)

// 通用参数构造函数 - 只获取最新一条数据
function buildParams() {
  return {
    sort: [
      {
        direction: 'DESC',
        property: 'createdAt',
      },
    ],
    page: 0, // 第一页
    size: 1, // 只取一条
    tenantId: Number(userStore.tenantId),
  }
}

// 功能模块参数构造函数 - 获取4条数据
function buildModulesParams(page = 0) {
  return {
    sort: [
      {
        direction: 'DESC',
        property: 'createdAt',
      },
    ],
    page, // 当前页码
    size: 4, // 显示4条
    tenantId: Number(userStore.tenantId),
  }
}

// 获取企业高级版数据
async function getEnterpriseData() {
  try {
    const params = buildParams()
    const response = await subscriptionsApi.getLicenseSubscribeList(params)

    if (response) {
      enterpriseData.value = response.content[0] // 取第一条（最新）数据
    }
  }
  catch (error) {
    console.error('获取企业高级版数据失败:', error)
    ElMessage.error('获取企业高级版数据失败')
  }
}

// 获取许可信息数据
async function getLicenseData() {
  try {
    const params = buildParams()
    const response = await subscriptionsApi.getLicenseList(params)

    if (response) {
      licenseData.value = response.content[0] // 取第一条（最新）数据
    }
  }
  catch (error) {
    console.error('获取许可信息数据失败:', error)
    ElMessage.error('获取许可信息数据失败')
  }
}

// 获取账单状态数据
async function getBillData() {
  try {
    const params = buildParams()
    const response = await subscriptionsApi.getPayOrderList(params)

    if (response) {
      billData.value = response.content[0] // 取第一条（最新）数据
    }
  }
  catch (error) {
    console.error('获取账单状态数据失败:', error)
    ElMessage.error('获取账单状态数据失败')
  }
}

// 获取已启用功能模块数据
async function getEnabledModules(page = 0) {
  try {
    modulesLoading.value = true
    const params = buildModulesParams(page)
    const response = await subscriptionsApi.enableModule(params)

    if (response) {
      enabledModules.value = response.content || [] // 获取功能模块列表
      currentModulePage.value = page
      // 计算总页数
      totalModulePages.value = response.totalPages || Math.ceil((response.totalElements || 0) / 4)
    }
  }
  catch (error) {
    console.error('获取已启用功能模块失败:', error)
    ElMessage.error('获取已启用功能模块失败')
  }
  finally {
    modulesLoading.value = false
  }
}

// 功能模块分页切换
function prevModulePage() {
  if (currentModulePage.value > 0) {
    getEnabledModules(currentModulePage.value - 1)
  }
}

function nextModulePage() {
  if (currentModulePage.value < totalModulePages.value - 1) {
    getEnabledModules(currentModulePage.value + 1)
  }
}

// 获取配额使用情况数据
async function getQuotaData() {
  try {
    quotaLoading.value = true
    const response = await subscriptionsApi.checkQuota(userStore.tenantId)

    if (response) {
      quotaData.value = response.data || response
    }
  }
  catch (error) {
    console.error('获取配额使用情况失败:', error)
    ElMessage.error('获取配额使用情况失败')
  }
  finally {
    quotaLoading.value = false
  }
}

// 刷新所有数据
async function refreshCurrentTab() {
  loading.value = true
  try {
    await Promise.all([
      getEnterpriseData(),
      getLicenseData(),
      getBillData(),
      getEnabledModules(),
      getQuotaData(),
    ])
  }
  finally {
    loading.value = false
  }
}

// 计算属性 - 格式化显示数据
const formattedEnterpriseData = computed(() => {
  if (!enterpriseData.value) { return null }

  // 计算剩余天数
  const calculateRemainingDays = () => {
    if (enterpriseData.value.cycleEnd) {
      const endDate = new Date(enterpriseData.value.cycleEnd)
      const today = new Date()
      const diffTime = endDate.getTime() - today.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return Math.max(0, diffDays)
    }
    return 0
  }

  // 解析产品包信息
  const parseMetadata = () => {
    try {
      return enterpriseData.value.metadata ? JSON.parse(enterpriseData.value.metadata) : {}
    }
    catch {
      return {}
    }
  }

  const metadata = parseMetadata()
  console.log(enterpriseData.value)
  return {
    ...enterpriseData.value,
    statusText: enterpriseData.value.statusDesc || (enterpriseData.value.status === 1 ? '正常' : '异常'),
    validPeriod: `${enterpriseData.value.cycleStart}至${enterpriseData.value.cycleEnd}`,
    remainingDays: calculateRemainingDays(),
    packageName: metadata.package,
    subscriptionType: enterpriseData.value.subscriptionModeDesc,
    renewalType: enterpriseData.value.renewalMethodDesc,
    price: enterpriseData.value.realPrice,
    orderId: enterpriseData.value.orderId,
    subscriptionCode: enterpriseData.value.subscriptionCode,
  }
})

const formattedLicenseData = computed(() => {
  if (!licenseData.value) { return null }

  // 根据许可证状态确定状态文本和颜色
  const getLicenseStatus = () => {
    switch (licenseData.value.status) {
      case 0: return { text: '未激活', color: 'warning' }
      case 1: return { text: '已激活', color: 'success' }
      case 2: return { text: '已过期', color: 'danger' }
      case 3: return { text: '已禁用', color: 'info' }
      default: return { text: '未知状态', color: 'info' }
    }
  }

  // 根据许可证类型确定类型文本
  const getLicenseType = () => {
    switch (licenseData.value.licenseType) {
      case 1: return '标准版'
      case 2: return '专业版'
      case 3: return '企业版'
      case 4: return '旗舰版'
      default: return '未知类型'
    }
  }

  // 计算剩余天数的状态
  const getDaysStatus = () => {
    const days = licenseData.value.leftDays || 0
    if (days <= 7) { return 'danger' }
    if (days <= 30) { return 'warning' }
    return 'success'
  }

  const licenseStatus = getLicenseStatus()

  return {
    ...licenseData.value,
    statusText: licenseStatus.text,
    statusColor: licenseStatus.color,
    licenseTypeText: getLicenseType(),
    daysStatus: getDaysStatus(),
    validPeriod: licenseData.value.startAt && licenseData.value.endAt
      ? `${new Date(licenseData.value.startAt).toLocaleDateString()} 至 ${new Date(licenseData.value.endAt).toLocaleDateString()}`
      : 'N/A',
    purchaseDate: licenseData.value.purchaseAt
      ? new Date(licenseData.value.purchaseAt).toLocaleDateString()
      : 'N/A',
    isExpired: licenseData.value.endAt && new Date(licenseData.value.endAt) < new Date(),
    isExpiringSoon: (licenseData.value.leftDays || 0) <= 30,
  }
})

const formattedBillData = computed(() => {
  if (!billData.value) { return null }

  // 根据支付状态确定状态文本和颜色
  const getPaymentStatus = () => {
    switch (billData.value.state) {
      case 0: return { text: '待支付', color: 'warning' }
      case 1: return { text: '支付成功', color: 'success' }
      case 2: return { text: '支付失败', color: 'danger' }
      case 3: return { text: '已取消', color: 'info' }
      case 4: return { text: '已退款', color: 'info' }
      case 5: return { text: '已关闭', color: 'info' }
      default: return { text: '未知状态', color: 'info' }
    }
  }

  // 根据退款状态确定退款文本
  const getRefundStatus = () => {
    switch (billData.value.refundState) {
      case 0: return '无退款'
      case 1: return '部分退款'
      case 2: return '全额退款'
      default: return '无退款'
    }
  }

  const paymentStatus = getPaymentStatus()

  return {
    ...billData.value,
    statusText: paymentStatus.text,
    statusColor: paymentStatus.color,
    refundStatusText: getRefundStatus(),
    formattedAmount: billData.value.amount || 0,
    formattedRefundAmount: billData.value.refundAmount || 0,
    isExpired: billData.value.expiredTime && new Date(billData.value.expiredTime) < new Date(),
    expiredDate: billData.value.expiredTime
      ? new Date(billData.value.expiredTime).toLocaleString()
      : null,
    successDate: billData.value.successTime
      ? new Date(billData.value.successTime).toLocaleString()
      : null,
    createdDate: billData.value.createdAt
      ? new Date(billData.value.createdAt).toLocaleString()
      : null,
  }
})

// 格式化功能模块数据
const formattedEnabledModules = computed(() => {
  if (!enabledModules.value || enabledModules.value.length === 0) {
    return []
  }

  return enabledModules.value.map((module: any) => {
    return {
      ...module,
      usageTypeText: module.usageType === 'duration' ? '时长' : module.usageType === 'quota' ? '次数' : (module.usageType || '未知'),
      featureName: module.featureName || '未知功能',
    }
  })
})

// 格式化配额数据
const formattedQuotaData = computed(() => {
  if (!quotaData.value) {
    return null
  }

  const data = quotaData.value

  // 计算使用率百分比
  const usagePercentage = data.quotaTotal > 0
    ? Math.round((data.quotaUsed / data.quotaTotal) * 100)
    : 0

  // 确定状态颜色
  const getStatusColor = () => {
    if (!data.sufficient) { return 'danger' }
    if (usagePercentage >= 80) { return 'warning' }
    return 'success'
  }

  // 确定状态文本
  const getStatusText = () => {
    if (!data.sufficient) { return '配额不足' }
    if (usagePercentage >= 80) { return '配额紧张' }
    return '配额充足'
  }

  return {
    ...data,
    usagePercentage,
    statusColor: getStatusColor(),
    statusText: getStatusText(),
    formattedQuotaTotal: data.quotaTotal?.toLocaleString() || '0',
    formattedQuotaUsed: data.quotaUsed?.toLocaleString() || '0',
    formattedQuotaRemaining: data.quotaRemaining?.toLocaleString() || '0',
  }
})

// 页面初始化
onMounted(() => {
  refreshCurrentTab()
})
</script>

<template>
  <div class="min-h-screen flex bg-gray-50">
    <!-- 主内容区 -->
    <div class="flex-1 p-8">
      <!-- 顶部标题和操作 -->
      <div class="mb-6 flex items-center justify-between">
        <div>
          <div class="mb-1 text-sm text-gray-500">
            系统管理 > 我的订阅
          </div>
          <h1 class="text-2xl text-gray-800 font-bold">
            我的订阅
          </h1>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-500">
            最后更新: 2023-11-15 14:30
          </div>
          <button class="flex items-center text-blue-600 hover:text-blue-800" @click="refreshCurrentTab">
            <el-icon class="mr-1">
              <Refresh />
            </el-icon>
            刷新
          </button>
          <a href="#" class="text-blue-600 hover:text-blue-800">查看帮助</a>
        </div>
      </div>

      <!-- 通知区域 -->
      <div class="mb-8 space-y-3">
        <!-- 订阅到期提醒 -->
        <div
          v-if="(formattedEnterpriseData?.remainingDays || 0) <= 30"
          class="flex items-center justify-between border-l-4 p-4"
          :class="(formattedEnterpriseData?.remainingDays || 0) <= 7 ? 'border-red-500 bg-red-50' : 'border-orange-500 bg-orange-50'"
        >
          <div class="flex items-center">
            <el-icon class="mr-2" :class="(formattedEnterpriseData?.remainingDays || 0) <= 7 ? 'text-red-500' : 'text-orange-500'">
              <Warning />
            </el-icon>
            <span>您的{{ formattedEnterpriseData?.packageName || '企业高级版' }}订阅即将在 {{ formattedEnterpriseData?.remainingDays || 0 }} 天后到期，请及时续订</span>
          </div>
          <el-icon class="cursor-pointer" :class="(formattedEnterpriseData?.remainingDays || 0) <= 7 ? 'text-red-500' : 'text-orange-500'">
            <Close />
          </el-icon>
        </div>

        <!-- 许可证到期提醒 -->
        <div
          v-if="formattedLicenseData?.isExpiringSoon"
          class="flex items-center justify-between border-l-4 p-4"
          :class="(formattedLicenseData?.leftDays || 0) <= 7 ? 'border-red-500 bg-red-50' : 'border-yellow-500 bg-yellow-50'"
        >
          <div class="flex items-center">
            <el-icon class="mr-2" :class="(formattedLicenseData?.leftDays || 0) <= 7 ? 'text-red-500' : 'text-yellow-500'">
              <Warning />
            </el-icon>
            <span>您的{{ formattedLicenseData?.licenseTypeText }}许可证即将在 {{ formattedLicenseData?.leftDays || 0 }} 天后到期</span>
          </div>
          <el-icon class="cursor-pointer" :class="(formattedLicenseData?.leftDays || 0) <= 7 ? 'text-red-500' : 'text-yellow-500'">
            <Close />
          </el-icon>
        </div>

        <!-- 许可证状态异常提醒 -->
        <div
          v-if="formattedLicenseData?.status === 0 || formattedLicenseData?.status === 2 || formattedLicenseData?.status === 3"
          class="flex items-center justify-between border-l-4 border-red-500 bg-red-50 p-4"
        >
          <div class="flex items-center">
            <el-icon class="mr-2 text-red-500">
              <Warning />
            </el-icon>
            <span>您的许可证状态异常：{{ formattedLicenseData?.statusText }}，请及时处理</span>
          </div>
          <el-icon class="cursor-pointer text-red-500">
            <Close />
          </el-icon>
        </div>

        <!-- 手动续费提醒 -->
        <div
          v-if="formattedEnterpriseData?.renewalMethod === 0"
          class="flex items-center justify-between border-l-4 border-blue-500 bg-blue-50 p-4"
        >
          <div class="flex items-center">
            <el-icon class="mr-2 text-blue-500">
              <InfoFilled />
            </el-icon>
            <span>您当前为手动续费模式，建议开启自动续费以避免服务中断</span>
          </div>
          <el-icon class="cursor-pointer text-blue-500">
            <Close />
          </el-icon>
        </div>
      </div>

      <!-- 订阅状态概览 -->
      <div v-loading="loading" class="grid grid-cols-1 mb-8 gap-6 lg:grid-cols-3">
        <!-- 当前订阅卡片 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <div class="mb-4 flex items-start justify-between">
            <div>
              <h2 class="text-xl text-gray-800 font-bold">
                {{ formattedEnterpriseData?.packageName || '企业高级版' }}
              </h2>
              <span
                class="mt-1 inline-block rounded-full px-2 py-1 text-xs"
                :class="formattedEnterpriseData?.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
              >
                {{ formattedEnterpriseData?.statusText || '正常' }}
              </span>
            </div>
            <el-icon class="text-2xl text-blue-500">
              <Medal />
            </el-icon>
          </div>
          <div class="space-y-3">
            <div>
              <div class="text-sm text-gray-500">
                订阅周期
              </div>
              <div>{{ formattedEnterpriseData?.validPeriod || '2023-05-20 至 2023-11-20' }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                剩余天数
              </div>
              <div
                class="text-lg font-semibold"
                :class="(formattedEnterpriseData?.remainingDays || 0) <= 30 ? 'text-red-500' : 'text-green-500'"
              >
                {{ formattedEnterpriseData?.remainingDays || 0 }} 天
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                订阅类型 / 续费方式
              </div>
              <div class="flex items-center justify-between">
                <span>{{ formattedEnterpriseData?.subscriptionType || '年度' }}</span>
                <span class="text-sm text-gray-600">{{ formattedEnterpriseData?.renewalType || '手动续费' }}</span>
              </div>
            </div>
            <div class="border-t pt-2">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <el-icon class="mr-2" :class="formattedEnterpriseData?.renewalMethod === 1 ? 'text-green-500' : 'text-orange-500'">
                    <CircleCheck v-if="formattedEnterpriseData?.renewalMethod === 1" />
                    <Warning v-else />
                  </el-icon>
                  <span>{{ formattedEnterpriseData?.renewalMethod === 1 ? '自动续订已开启' : '手动续费模式' }}</span>
                </div>
                <div class="text-sm text-gray-500">
                  ¥{{ (formattedEnterpriseData?.price || 0) / 100 }}
                </div>
              </div>
            </div>
            <div v-if="formattedEnterpriseData?.orderId" class="text-xs text-gray-400">
              订单号: {{ formattedEnterpriseData.orderId }} | 订阅码: {{ formattedEnterpriseData.subscriptionCode }}
            </div>
          </div>
        </div>

        <!-- 许可信息卡片 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <div class="mb-4 flex items-start justify-between">
            <div>
              <h2 class="text-xl text-gray-800 font-bold">
                许可信息
              </h2>
              <span
                class="mt-1 inline-block rounded-full px-2 py-1 text-xs"
                :class="`bg-${formattedLicenseData?.statusColor || 'gray'}-100 text-${formattedLicenseData?.statusColor || 'gray'}-800`"
              >
                {{ formattedLicenseData?.statusText || '未知状态' }}
              </span>
            </div>
            <el-icon class="text-2xl text-green-500">
              <Lock />
            </el-icon>
          </div>
          <div class="space-y-4">
            <div>
              <div class="text-sm text-gray-500">
                许可证编号
              </div>
              <div class="text-sm font-mono">
                {{ formattedLicenseData?.licenseCode || 'N/A' }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                许可证类型
              </div>
              <div class="font-semibold">
                {{ formattedLicenseData?.licenseTypeText || '未知类型' }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                有效期
              </div>
              <div>{{ formattedLicenseData?.validPeriod || 'N/A' }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                剩余天数
              </div>
              <div
                class="text-lg font-semibold"
                :class="`text-${formattedLicenseData?.daysStatus || 'gray'}-500`"
              >
                {{ formattedLicenseData?.leftDays || 0 }} 天
              </div>
            </div>
            <div v-if="formattedLicenseData?.tenantName" class="border-t pt-3">
              <div class="text-sm text-gray-500">
                租户信息
              </div>
              <div class="text-sm">
                {{ formattedLicenseData.tenantName }}
              </div>
            </div>
            <div class="text-xs text-gray-400">
              购买时间: {{ formattedLicenseData?.purchaseDate || 'N/A' }}
            </div>
            <a href="#" class="inline-block text-blue-600 hover:text-blue-800">管理许可</a>
          </div>
        </div>

        <!-- 账单状态卡片 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <h2 class="mb-4 text-xl text-gray-800 font-bold">
            账单状态
          </h2>
          <div class="space-y-4">
            <div>
              <div class="text-sm text-gray-500">
                支付订单号
              </div>
              <div class="text-sm font-mono">
                {{ formattedBillData?.payOrderId || 'N/A' }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                订单金额
              </div>
              <div class="text-lg text-green-600 font-semibold">
                ¥{{ formattedBillData?.formattedAmount || 0 }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                支付状态
              </div>
              <div class="flex items-center">
                <el-tag
                  :type="formattedBillData?.statusColor || 'info'"
                  size="small"
                  class="mr-2"
                >
                  {{ formattedBillData?.statusText || '未知状态' }}
                </el-tag>
                <span v-if="formattedBillData?.isExpired" class="text-xs text-red-500">(已过期)</span>
              </div>
            </div>
            <div v-if="formattedBillData?.refundAmount > 0">
              <div class="text-sm text-gray-500">
                退款信息
              </div>
              <div class="text-sm">
                <span class="text-orange-600">{{ formattedBillData?.refundStatusText }}</span>
                <span class="ml-2">¥{{ formattedBillData?.formattedRefundAmount }}</span>
              </div>
            </div>
            <div class="border-t pt-3">
              <div class="text-xs text-gray-500 space-y-1">
                <div>创建时间: {{ formattedBillData?.createdDate || 'N/A' }}</div>
                <div v-if="formattedBillData?.successDate">
                  支付时间: {{ formattedBillData.successDate }}
                </div>
                <div v-if="formattedBillData?.expiredDate">
                  过期时间: {{ formattedBillData.expiredDate }}
                </div>
              </div>
            </div>
            <a href="#" class="inline-block text-blue-600 hover:text-blue-800">查看详细账单</a>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="mb-8 flex space-x-4">
        <el-button type="primary">
          续订套餐
        </el-button>
      </div>

      <!-- 功能与使用区 -->
      <div class="grid grid-cols-1 mb-8 gap-6 lg:grid-cols-2">
        <!-- 已启用功能模块 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-xl text-gray-800 font-bold">
              已启用功能模块
            </h2>
            <div class="flex items-center space-x-2">
              <button
                :disabled="currentModulePage <= 0"
                class="rounded p-1 disabled:cursor-not-allowed hover:bg-gray-100 disabled:opacity-50"
                @click="prevModulePage"
              >
                <el-icon><ArrowLeft /></el-icon>
              </button>
              <span class="text-sm text-gray-500">
                {{ currentModulePage + 1 }} / {{ Math.max(1, totalModulePages) }}
              </span>
              <button
                :disabled="currentModulePage >= totalModulePages - 1"
                class="rounded p-1 disabled:cursor-not-allowed hover:bg-gray-100 disabled:opacity-50"
                @click="nextModulePage"
              >
                <el-icon><ArrowRight /></el-icon>
              </button>
            </div>
          </div>
          <div v-loading="modulesLoading" class="grid grid-cols-2 gap-4">
            <!-- 动态功能模块卡片 -->
            <div
              v-for="module in formattedEnabledModules"
              :key="module.id"
              class="border rounded p-3"
            >
              <div class="mb-2 flex items-center">
                <el-icon class="mr-2 text-green-500">
                  <CircleCheck />
                </el-icon>
                <span class="font-medium">{{ module.featureName }}</span>
              </div>
              <div class="text-sm text-gray-500">
                权限类型: {{ module.usageTypeDesc }}
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!modulesLoading && formattedEnabledModules.length === 0"
              class="col-span-2 py-8 text-center text-gray-500"
            >
              <el-icon class="mb-2 text-4xl">
                <Box />
              </el-icon>
              <div>暂无已启用的功能模块</div>
            </div>
          </div>
        </div>

        <!-- 使用情况概览 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-xl text-gray-800 font-bold">
              使用情况概览
            </h2>
            <button
              :disabled="quotaLoading"
              class="text-blue-600 hover:text-blue-800 disabled:opacity-50"
              @click="getQuotaData"
            >
              <el-icon class="mr-1">
                <Refresh />
              </el-icon>
              刷新
            </button>
          </div>

          <div v-loading="quotaLoading" class="space-y-6">
            <!-- 配额状态总览 -->
            <div v-if="formattedQuotaData" class="border rounded-lg p-4">
              <div class="mb-3 flex items-center justify-between">
                <div class="flex items-center">
                  <el-icon
                    class="mr-2 text-lg" :class="{
                      'text-green-500': formattedQuotaData.statusColor === 'success',
                      'text-orange-500': formattedQuotaData.statusColor === 'warning',
                      'text-red-500': formattedQuotaData.statusColor === 'danger',
                    }"
                  >
                    <CircleCheck v-if="formattedQuotaData.sufficient" />
                    <Warning v-else />
                  </el-icon>
                  <span class="text-lg font-medium">{{ formattedQuotaData.featureName || '系统配额' }}</span>
                </div>
                <el-tag
                  :type="formattedQuotaData.statusColor"
                  size="small"
                >
                  {{ formattedQuotaData.statusText }}
                </el-tag>
              </div>

              <!-- 配额使用进度条 -->
              <div class="mb-4">
                <div class="mb-2 flex justify-between text-sm">
                  <span>配额使用情况</span>
                  <span>{{ formattedQuotaData.usagePercentage }}%</span>
                </div>
                <div class="h-3 w-full rounded-full bg-gray-200">
                  <div
                    class="h-3 rounded-full transition-all duration-300"
                    :class="{
                      'bg-green-500': formattedQuotaData.statusColor === 'success',
                      'bg-orange-500': formattedQuotaData.statusColor === 'warning',
                      'bg-red-500': formattedQuotaData.statusColor === 'danger',
                    }"
                    :style="{ width: `${formattedQuotaData.usagePercentage}%` }"
                  />
                </div>
              </div>

              <!-- 配额详细信息 -->
              <div class="grid grid-cols-3 gap-4 text-center">
                <div class="border-r">
                  <div class="text-2xl text-blue-600 font-bold">
                    {{ formattedQuotaData.formattedQuotaTotal }}
                  </div>
                  <div class="text-sm text-gray-500">
                    总配额
                  </div>
                </div>
                <div class="border-r">
                  <div class="text-2xl text-orange-600 font-bold">
                    {{ formattedQuotaData.formattedQuotaUsed }}
                  </div>
                  <div class="text-sm text-gray-500">
                    已使用
                  </div>
                </div>
                <div>
                  <div class="text-2xl text-green-600 font-bold">
                    {{ formattedQuotaData.formattedQuotaRemaining }}
                  </div>
                  <div class="text-sm text-gray-500">
                    剩余配额
                  </div>
                </div>
              </div>

              <!-- 功能权限编码 -->
              <div v-if="formattedQuotaData.featureCode" class="mt-4 border-t pt-4">
                <div class="text-xs text-gray-500">
                  功能编码: {{ formattedQuotaData.featureCode }}
                </div>
              </div>

              <!-- 不足原因 -->
              <div
                v-if="!formattedQuotaData.sufficient && formattedQuotaData.reason"
                class="mt-4 border border-red-200 rounded bg-red-50 p-3"
              >
                <div class="flex items-start">
                  <el-icon class="mr-2 mt-0.5 text-red-500">
                    <Warning />
                  </el-icon>
                  <div>
                    <div class="text-red-800 font-medium">
                      配额不足原因
                    </div>
                    <div class="text-sm text-red-600">
                      {{ formattedQuotaData.reason }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!quotaLoading && !formattedQuotaData"
              class="py-8 text-center text-gray-500"
            >
              <el-icon class="mb-2 text-4xl">
                <Document />
              </el-icon>
              <div>暂无配额使用数据</div>
              <button
                class="mt-2 text-blue-600 hover:text-blue-800"
                @click="getQuotaData"
              >
                点击获取数据
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 待处理事项区 -->
      <div v-if="false" class="grid grid-cols-1 mb-8 gap-6 lg:grid-cols-2">
        <!-- 待处理任务 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-xl text-gray-800 font-bold">
              待处理任务 (3)
            </h2>
            <a href="#" class="text-blue-600 hover:text-blue-800">查看全部</a>
          </div>
          <div class="space-y-4">
            <div v-if="(formattedEnterpriseData?.remainingDays || 0) <= 30" class="border-b pb-3">
              <div class="mb-1 flex items-center justify-between">
                <div class="flex items-center">
                  <el-icon class="mr-2" :class="(formattedEnterpriseData?.remainingDays || 0) <= 7 ? 'text-red-500' : 'text-orange-500'">
                    <Bell />
                  </el-icon>
                  <span class="font-medium">{{ formattedEnterpriseData?.packageName || '企业高级版' }}订阅即将到期</span>
                </div>
                <span class="text-sm text-gray-500">{{ (formattedEnterpriseData?.remainingDays || 0) <= 7 ? '高优先级' : '中优先级' }}</span>
              </div>
              <div class="ml-7 text-sm text-gray-600">
                您的{{ formattedEnterpriseData?.subscriptionType || '年度' }}订阅将在{{ formattedEnterpriseData?.remainingDays || 0 }}天后到期，请及时处理
              </div>
              <div class="mt-2 flex items-center justify-between">
                <span class="text-sm text-gray-500">到期时间: {{ formattedEnterpriseData?.validPeriod?.split(' 至 ')[1] || '2023-11-20' }}</span>
                <button class="text-sm text-blue-600 hover:text-blue-800">
                  {{ formattedEnterpriseData?.renewalMethod === 1 ? '查看续费' : '立即续费' }}
                </button>
              </div>
            </div>
            <div v-if="formattedLicenseData?.isExpiringSoon || formattedLicenseData?.status !== 1" class="border-b pb-3">
              <div class="mb-1 flex items-center justify-between">
                <div class="flex items-center">
                  <el-icon class="mr-2" :class="formattedLicenseData?.status !== 1 ? 'text-red-500' : (formattedLicenseData?.leftDays || 0) <= 7 ? 'text-red-500' : 'text-yellow-500'">
                    <Warning />
                  </el-icon>
                  <span class="font-medium">
                    {{ formattedLicenseData?.status !== 1 ? '许可证状态异常' : '许可证即将到期' }}
                  </span>
                </div>
                <span class="text-sm text-gray-500">
                  {{ formattedLicenseData?.status !== 1 ? '高优先级' : (formattedLicenseData?.leftDays || 0) <= 7 ? '高优先级' : '中优先级' }}
                </span>
              </div>
              <div class="ml-7 text-sm text-gray-600">
                {{ formattedLicenseData?.status !== 1
                  ? `许可证状态为"${formattedLicenseData?.statusText}"，需要处理`
                  : `${formattedLicenseData?.licenseTypeText}许可证将在${formattedLicenseData?.leftDays}天后到期` }}
              </div>
              <div class="mt-2 flex items-center justify-between">
                <span class="text-sm text-gray-500">许可证: {{ formattedLicenseData?.licenseCode }}</span>
                <button class="text-sm text-blue-600 hover:text-blue-800">
                  {{ formattedLicenseData?.status !== 1 ? '立即处理' : '续期许可' }}
                </button>
              </div>
            </div>
            <div v-if="formattedBillData && formattedBillData.state === 0">
              <div class="mb-1 flex items-center justify-between">
                <div class="flex items-center">
                  <el-icon class="mr-2" :class="formattedBillData.isExpired ? 'text-red-500' : 'text-blue-500'">
                    <Document />
                  </el-icon>
                  <span class="font-medium">{{ formattedBillData.isExpired ? '账单已过期' : '账单待支付' }}</span>
                </div>
                <span class="text-sm text-gray-500">{{ formattedBillData.isExpired ? '高优先级' : '中优先级' }}</span>
              </div>
              <div class="ml-7 text-sm text-gray-600">
                订单 {{ formattedBillData.payOrderId }} 金额 ¥{{ formattedBillData.formattedAmount }}，{{ formattedBillData.isExpired ? '已过期需重新处理' : '请及时支付' }}
              </div>
              <div class="mt-2 flex items-center justify-between">
                <span class="text-sm text-gray-500">{{ formattedBillData.isExpired ? '过期时间' : '到期时间' }}: {{ formattedBillData.expiredDate }}</span>
                <button class="text-sm text-blue-600 hover:text-blue-800">
                  {{ formattedBillData.isExpired ? '重新支付' : '立即支付' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近订阅活动 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-xl text-gray-800 font-bold">
              最近订阅活动
            </h2>
            <a href="#" class="text-blue-600 hover:text-blue-800">查看完整历史</a>
          </div>
          <div class="space-y-4">
            <div v-if="enterpriseData" class="border-b pb-3">
              <div class="mb-1 flex justify-between">
                <span class="font-medium">{{ formattedEnterpriseData?.subscriptionType || '年度' }}订阅激活</span>
                <span class="text-sm text-gray-500">{{ enterpriseData.createdAt || '2023-05-20 14:30' }}</span>
              </div>
              <div class="text-sm text-gray-600">
                {{ formattedEnterpriseData?.packageName || '企业高级版' }}套餐已成功激活，订单号: {{ formattedEnterpriseData?.orderId }}
              </div>
              <div class="mt-1 text-xs text-gray-500">
                金额: ¥{{ (formattedEnterpriseData?.price || 0) / 100 }} | 续费方式: {{ formattedEnterpriseData?.renewalType }}
              </div>
            </div>
            <div v-if="licenseData" class="border-b pb-3">
              <div class="mb-1 flex justify-between">
                <span class="font-medium">
                  {{ formattedLicenseData?.status === 1 ? '许可证激活' : '许可证配置' }}
                </span>
                <span class="text-sm text-gray-500">{{ formattedLicenseData?.purchaseDate || '2023-04-15' }}</span>
              </div>
              <div class="text-sm text-gray-600">
                {{ formattedLicenseData?.licenseTypeText }}许可证已{{ formattedLicenseData?.status === 1 ? '激活' : '配置' }}，租户: {{ formattedLicenseData?.tenantName }}
              </div>
              <div class="mt-1 text-xs text-gray-500">
                许可证编号: {{ formattedLicenseData?.licenseCode }} | 状态: {{ formattedLicenseData?.statusText }}
                <span v-if="formattedLicenseData?.leftDays !== undefined" class="ml-2">
                  | 剩余: {{ formattedLicenseData.leftDays }}天
                </span>
              </div>
            </div>
            <div v-if="billData" class="border-b pb-3">
              <div class="mb-1 flex justify-between">
                <span class="font-medium">
                  {{ formattedBillData?.state === 1 ? '支付成功' : formattedBillData?.state === 0 ? '订单创建' : '订单处理' }}
                </span>
                <span class="text-sm text-gray-500">
                  {{ formattedBillData?.state === 1 && formattedBillData?.successDate ? formattedBillData.successDate : billData.createdAt }}
                </span>
              </div>
              <div class="text-sm text-gray-600">
                订单 {{ formattedBillData?.payOrderId }} 金额 ¥{{ formattedBillData?.formattedAmount }}，状态: {{ formattedBillData?.statusText }}
              </div>
              <div class="mt-1 text-xs text-gray-500">
                {{ formattedBillData?.state === 0 ? '等待支付' : formattedBillData?.state === 1 ? '支付完成' : '订单异常' }}
                <span v-if="formattedBillData?.refundAmount > 0" class="ml-2 text-orange-600">
                  | 退款: ¥{{ formattedBillData.formattedRefundAmount }}
                </span>
              </div>
            </div>
            <div v-if="!enterpriseData && !licenseData && !billData">
              <div class="py-4 text-center text-gray-500">
                暂无订阅活动记录
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
