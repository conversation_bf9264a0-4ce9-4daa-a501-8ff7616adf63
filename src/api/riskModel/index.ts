import api from '../index'

// 风险模型管理API
const riskModelApi = {
  // 获取风险模型列表
  getRiskModels(data: any) {
    return api({
      url: '/whiskerguardorgservice/api/risk/models/page',
      method: 'post',
      data,
    })
  },

  // 创建风险模型
  createRiskModel(data: any) {
    return api({
      url: '/whiskerguardorgservice/api/risk/models',
      method: 'post',
      data,
    })
  },

  // 更新风险模型
  updateRiskModel(data: any) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${data.id}`,
      method: 'patch',
      data,
    })
  },

  // 删除风险模型
  deleteRiskModel(id: number) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${id}`,
      method: 'delete',
    })
  },

  // 获取风险模型详情
  getRiskModelDetail(id: number) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${id}`,
      method: 'get',
    })
  },

  // 设置默认模型
  setDefaultModel(id: number, data: any) {
    return api({
      url: `/whiskerguardorgservice/api/risk/models/${id}`,
      method: 'patch',
      data,
    })
  },
  // 新增风险分类
  createRiskCategory(data: any) {
    return api({
      url: '/whiskerguardorgservice/api/risk/categories',
      method: 'post',
      data,
    })
  },

  // 更新风险分类
  updateRiskCategory(data: any) {
    return api({
      url: `/whiskerguardorgservice/api/risk/categories/${data.id}`,
      method: 'patch',
      data,
    })
  },

  // 获取所有风险规则
  getAllRiskRules(params: any) {
    return api({
      url: `/whiskerguardorgservice/api/risk/rules?page=${params.page}&size=${params.size}&riskCategoryId=${params.riskCategoryId}`,
      method: 'get',
    })
  },

  // 删除风险规则
  deleteRiskRule(id: number) {
    return api({
      url: `/whiskerguardorgservice/api/risk/rules/${id}`,
      method: 'delete',
    })
  },

}

export default riskModelApi
