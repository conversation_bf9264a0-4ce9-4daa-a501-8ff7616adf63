<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import {
  ArrowDown, ChatDotRound, Close, Document, Download, Filter, Plus,
  Refresh, Search, Top, View, Wallet,
} from '@element-plus/icons-vue'
import api from '@/api/subscriptions'

// 定义订单类型 - 基于API接口
interface PayOrder {
  id: number
  tenantId: number
  payOrderId: string // 支付订单号
  wayCode: number // 支付方式代码
  amount: number // 支付金额
  mchFeeRate: number // 商户手续费费率快照
  mchFeeAmount: number // 商户手续费,单位分
  state: number // 支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭
  clientIp: string // 客户端IP
  licenseSubscriptionCode: string // license订阅code
  serviceSubscriptionCodes: string // 增值服务订阅code
  subject: string // 商品标题
  body: string // 商品描述信息
  channelUser: string // 渠道用户标识
  channelOrderNo: string // 渠道订单号
  refundState: number // 退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款
  refundTimes: number // 退款次数
  refundAmount: number // 退款总金额,单位分
  errCode: string // 渠道支付错误码
  errMsg: string // 渠道支付错误描述
  extParam: string // 商户扩展参数
  returnUrl: string // 页面跳转地址
  expiredTime: {
    dateTime: string
    offset: {
      totalSeconds: number
    }
    zone: any
  } | null // 订单失效时间
  successTime: string | null // 订单支付成功时间
  metadata: any | null // 附加信息
  createdAt: string | null // 创建时间
  createdBy: string | null // 创建人
  updatedAt: string | null // 更新时间
  updatedBy: string | null // 更新人
  isDeleted: boolean | null // 删除状态
}

// 订单概览数据
const stats = [
  { title: '全部订单', value: '1,248', trendValue: '+12.5%', trendClass: 'bg-green-50 text-green-600', trendIcon: 'Top', trendIconClass: 'text-green-500' },
  { title: '待支付订单', value: '23', trendValue: '+8.3%', trendClass: 'bg-red-50 text-red-600', trendIcon: 'Top', trendIconClass: 'text-red-500' },
  { title: '处理中订单', value: '15', trendValue: '-5.2%', trendClass: 'bg-blue-50 text-blue-600', trendIcon: 'Bottom', trendIconClass: 'text-blue-500' },
  { title: '已完成订单', value: '1,180', trendValue: '+15.7%', trendClass: 'bg-green-50 text-green-600', trendIcon: 'Top', trendIconClass: 'text-green-500' },
  { title: '已取消订单', value: '30', trendValue: '-2.1%', trendClass: 'bg-gray-50 text-gray-600', trendIcon: 'Bottom', trendIconClass: 'text-gray-500' },
]
// 移除未使用的变量
// 订单列表数据
const payOrderId = ref('')
const state = ref(undefined)
const startTime = ref(undefined)
const endTime = ref(undefined)
const minAmount = ref(null)
const maxAmount = ref(null)
const currentPage = ref(0)
const pageSize = ref(10)
const totalOrders = ref(0)
const selectedOrders = ref<PayOrder[]>([])
const orders = ref<PayOrder[]>([])
const loading = ref(false)

// 获取订单列表
async function getOrderList() {
  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      payOrderId: payOrderId.value || null,
      state: state.value,
      startTime: startTime.value,
      endTime: endTime.value,
      minAmount: Number(minAmount.value) || null,
      maxAmount: Number(maxAmount.value) || null,
    }

    const response = await api.getOrderList(params)
    if (response) {
      orders.value = response.content
      totalOrders.value = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取订单列表失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 搜索订单
function handleSearch() {
  currentPage.value = 1
  getOrderList()
}

// 重置搜索
function handleReset() {
  payOrderId.value = ''
  state.value = undefined
  startTime.value = undefined
  endTime.value = undefined
  minAmount.value = null
  maxAmount.value = null
  currentPage.value = 0
  getOrderList()
}

// 页面变化
function handlePageChange() {
  getOrderList()
}

// 页面大小变化
function handleSizeChange() {
  currentPage.value = 0
  getOrderList()
}

// 订单详情
const showOrderDetail = ref(false)
const currentOrder = ref<PayOrder | null>(null)

// 状态映射函数
function getStatusText(state: number): string {
  switch (state) {
    case 0: return '待支付'
    case 1: return '支付中'
    case 2: return '支付成功'
    case 3: return '支付失败'
    case 4: return '已撤销'
    case 5: return '已退款'
    case 6: return '订单关闭'
    default: return '未知状态'
  }
}

// 支付方式映射函数
function getPaymentMethodText(wayCode: number): string {
  switch (wayCode) {
    case 0: return '未知'
    case 1: return '企业网银'
    case 2: return '支付宝'
    case 3: return '微信支付'
    case 4: return '银行卡'
    default: return '其他'
  }
}

// 金额格式化函数（分转元）
function formatAmount(amount: number): string {
  return (amount / 100).toFixed(2)
}

// 方法
function statusTagType(state: number): 'warning' | 'success' | 'info' | 'primary' | 'danger' {
  switch (state) {
    case 0: return 'warning' // 待支付
    case 1: return 'primary' // 支付中
    case 2: return 'success' // 支付成功
    case 3: return 'danger' // 支付失败
    case 4: return 'info' // 已撤销
    case 5: return 'info' // 已退款
    case 6: return 'info' // 订单关闭
    default: return 'primary'
  }
}
function handleSelectionChange(val: PayOrder[]) {
  selectedOrders.value = val
}
function viewOrderDetail(order: PayOrder) {
  currentOrder.value = order
  showOrderDetail.value = true
}
function payOrder(_order: PayOrder) {
  // 实际业务中调用支付接口
}
function cancelOrder(_order: PayOrder) {
  // 实际业务中调用取消订单接口
}
function applyInvoice(_order: PayOrder) {
  // 实际业务中调用申请发票接口
}
function viewInvoice(_order: PayOrder) {
  // 实际业务中跳转到发票详情页
}
function contactSupport() {
  // 实际业务中调用客服接口
}

// 组件挂载时获取数据
onMounted(() => {
  getOrderList()
})
</script>

<template>
  <div class="min-h-screen flex bg-gray-50">
    <!-- 主内容区 -->
    <div class="flex-1 overflow-auto">
      <!-- 顶部面包屑 -->
      <div class="bg-white p-4 shadow-sm">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>系统管理</el-breadcrumb-item>
          <el-breadcrumb-item>订阅中心</el-breadcrumb-item>
          <el-breadcrumb-item>订单管理</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <!-- 订单概览区 -->
      <div class="p-6">
        <div class="mb-6 rounded-lg bg-white p-6 shadow">
          <div class="mb-6 flex items-center justify-between">
            <h2 class="text-xl text-gray-800 font-semibold">
              订单概览
            </h2>
            <div class="flex space-x-2">
              <el-button class="!rounded-button whitespace-nowrap">
                <el-icon class="mr-1">
                  <Download />
                </el-icon>导出数据
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap" @click="getOrderList">
                <el-icon class="mr-1">
                  <Refresh />
                </el-icon>刷新
              </el-button>
            </div>
          </div>
          <!-- 统计卡片 -->
          <div class="grid grid-cols-5 mb-6 gap-4">
            <div
              v-for="stat in stats" :key="stat.title"
              class="border rounded-lg bg-white p-4 transition-shadow hover:shadow-md"
            >
              <div class="flex items-start justify-between">
                <div>
                  <div class="text-sm text-gray-500">
                    {{ stat.title }}
                  </div>
                  <div class="mt-1 text-2xl font-bold">
                    {{ stat.value }}
                  </div>
                </div>
                <div :class="stat.trendClass" class="flex items-center rounded-full px-2 py-1 text-xs">
                  <el-icon class="mr-1" :class="stat.trendIconClass">
                    <component :is="stat.trendIcon" />
                  </el-icon>
                  {{ stat.trendValue }}
                </div>
              </div>
              <div class="mt-4 h-12">
                <!-- 迷你图占位 -->
                <div class="h-full rounded bg-gray-100" />
              </div>
            </div>
          </div>
          <!-- 时间与金额统计 -->
          <div class="grid grid-cols-3 gap-4">
            <!-- 已删除消费趋势和消费统计卡片 -->
          </div>
        </div>
        <!-- 订单列表区 -->
        <div class="rounded-lg bg-white p-6 shadow">
          <!-- 筛选区域 -->
          <div class="mb-6">
            <div class="flex items-center space-x-4">
              <el-input v-model="payOrderId" placeholder="搜索订单号" class="w-64" size="default" clearable @keyup.enter="handleSearch">
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-select v-model="state" placeholder="全部状态" class="w-32" size="default" clearable>
                <el-option label="待支付" :value="0" />
                <el-option label="支付中" :value="1" />
                <el-option label="支付成功" :value="2" />
                <el-option label="支付失败" :value="3" />
                <el-option label="已撤销" :value="4" />
                <el-option label="已退款" :value="5" />
                <el-option label="订单关闭" :value="6" />
              </el-select>
              <el-date-picker
                v-model="startTime"
                type="date"
                placeholder="开始日期"
                value-format="YYYY-MM-DD"
                class="w-32"
                size="default"
              />
              <el-date-picker
                v-model="endTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="结束日期"
                class="w-32"
                size="default"
              />
              <el-input v-model="minAmount" placeholder="最低金额" class="w-24" size="default" />
              <span class="text-gray-400">-</span>
              <el-input v-model="maxAmount" placeholder="最高金额" class="w-24" size="default" />
              <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
                查询
              </el-button>
              <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">
                重置
              </el-button>
            </div>
          </div>
          <!-- 订单表格 -->
          <el-table v-loading="loading" :data="orders" style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="payOrderId" label="订单号" width="180" />
            <el-table-column prop="createdBy" label="创建人" width="180" />
            <el-table-column prop="createdAt" label="创建日期" width="150">
              <template #default="{ row }">
                {{ row.createdAt ? new Date(row.createdAt).toLocaleString() : '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="state" label="支付状态" width="120">
              <template #default="{ row }">
                <el-tag :type="statusTagType(row.state)" size="small">
                  {{ getStatusText(row.state) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="wayCode" label="支付方式" width="120">
              <template #default="{ row }">
                {{ getPaymentMethodText(row.wayCode) }}
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="订单金额" width="120">
              <template #default="{ row }">
                <div class="font-medium">
                  ¥{{ formatAmount(row.amount) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="250">
              <template #default="{ row }">
                <div class="flex space-x-2">
                  <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap" @click="viewOrderDetail(row)">
                    查看详情
                  </el-button>
                  <el-button v-if="row.state === 0" type="success" size="small" class="!rounded-button whitespace-nowrap" @click="payOrder(row)">
                    去支付
                  </el-button>
                  <el-button v-if="row.state === 0" type="danger" size="small" class="!rounded-button whitespace-nowrap" @click="cancelOrder(row)">
                    取消订单
                  </el-button>
                  <el-button v-if="row.state === 2 && row.refundState === 0" size="small" class="!rounded-button whitespace-nowrap" @click="applyInvoice(row)">
                    <el-icon class="mr-1">
                      <Document />
                    </el-icon>申请发票
                  </el-button>
                  <el-button v-if="row.state === 5" size="small" class="!rounded-button whitespace-nowrap" @click="viewInvoice(row)">
                    <el-icon class="mr-1">
                      <View />
                    </el-icon>查看退款
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-500">
              共 {{ totalOrders }} 条记录
            </div>
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalOrders"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 订单详情抽屉 -->
    <el-drawer v-model="showOrderDetail" title="订单详情" size="40%">
      <div v-if="currentOrder" class="p-6 space-y-6">
        <!-- 基本信息 -->
        <div class="rounded-lg bg-white p-4 shadow">
          <h3 class="mb-4 text-lg font-medium">
            基本信息
          </h3>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-gray-500">
                订单号
              </div>
              <div class="font-medium">
                {{ currentOrder.payOrderId }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                创建时间
              </div>
              <div class="font-medium">
                {{ currentOrder.createdAt ? new Date(currentOrder.createdAt).toLocaleString() : '--' }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                租户ID
              </div>
              <div class="font-medium">
                {{ currentOrder.tenantId }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                订单状态
              </div>
              <div>
                <el-tag :type="statusTagType(currentOrder.state)" size="small">
                  {{ getStatusText(currentOrder.state) }}
                </el-tag>
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                支付方式
              </div>
              <div class="font-medium">
                {{ getPaymentMethodText(currentOrder.wayCode) }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                支付时间
              </div>
              <div class="font-medium">
                {{ currentOrder.successTime ? new Date(currentOrder.successTime).toLocaleString() : '--' }}
              </div>
            </div>
          </div>
        </div>
        <!-- 订单内容 -->
        <div class="rounded-lg bg-white p-4 shadow">
          <h3 class="mb-4 text-lg font-medium">
            订单内容
          </h3>
          <div class="space-y-4">
            <div>
              <div class="text-sm text-gray-500">
                商品名称
              </div>
              <div class="font-medium">
                {{ currentOrder.subject }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                商品描述
              </div>
              <div class="font-medium">
                {{ currentOrder.body }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                订单金额
              </div>
              <div class="text-red-500 font-medium">
                ¥{{ formatAmount(currentOrder.amount) }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                订阅代码
              </div>
              <div class="font-medium">
                {{ currentOrder.licenseSubscriptionCode }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">
                增值服务
              </div>
              <div class="font-medium">
                {{ currentOrder.serviceSubscriptionCodes || '无' }}
              </div>
            </div>
          </div>
        </div>
        <!-- 金额明细 -->
        <div class="rounded-lg bg-white p-4 shadow">
          <h3 class="mb-4 text-lg font-medium">
            金额明细
          </h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <div class="text-gray-600">
                订单金额
              </div>
              <div class="font-medium">
                ¥{{ formatAmount(currentOrder.amount) }}
              </div>
            </div>
            <div class="flex justify-between">
              <div class="text-gray-600">
                商户费率
              </div>
              <div class="font-medium">
                {{ (currentOrder.mchFeeRate * 100).toFixed(2) }}%
              </div>
            </div>
            <div class="flex justify-between">
              <div class="text-gray-600">
                手续费金额
              </div>
              <div class="font-medium">
                ¥{{ formatAmount(currentOrder.mchFeeAmount) }}
              </div>
            </div>
            <div class="flex justify-between">
              <div class="text-gray-600">
                退款金额
              </div>
              <div class="text-red-500 font-medium">
                {{ currentOrder.refundAmount > 0 ? `-¥${formatAmount(currentOrder.refundAmount)}` : '¥0.00' }}
              </div>
            </div>
            <div class="flex justify-between border-t pt-2">
              <div class="text-gray-600 font-medium">
                实际支付
              </div>
              <div class="font-bold">
                ¥{{ formatAmount(currentOrder.amount) }}
              </div>
            </div>
          </div>
        </div>
        <!-- 操作按钮 -->
        <div class="flex space-x-3">
          <el-button v-if="currentOrder.state === 0" type="primary" class="!rounded-button flex-1 whitespace-nowrap" @click="payOrder(currentOrder)">
            <el-icon class="mr-1">
              <Wallet />
            </el-icon>去支付
          </el-button>
          <el-button v-if="currentOrder.state === 0" class="!rounded-button flex-1 whitespace-nowrap" @click="cancelOrder(currentOrder)">
            <el-icon class="mr-1">
              <Close />
            </el-icon>取消订单
          </el-button>
          <el-button v-if="currentOrder.state === 2 && currentOrder.refundState === 0" class="!rounded-button flex-1 whitespace-nowrap" @click="applyInvoice(currentOrder)">
            <el-icon class="mr-1">
              <Document />
            </el-icon>申请发票
          </el-button>
          <el-button v-if="currentOrder.refundState > 0" class="!rounded-button flex-1 whitespace-nowrap" @click="viewInvoice(currentOrder)">
            <el-icon class="mr-1">
              <View />
            </el-icon>查看退款
          </el-button>
          <el-button class="!rounded-button flex-1 whitespace-nowrap" @click="contactSupport">
            <el-icon class="mr-1">
              <ChatDotRound />
            </el-icon>联系客服
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.el-table {
--el-table-border-color: #f0f0f0;
--el-table-header-bg-color: #f8fafc;
}
.el-drawer__header {
@apply border-b pb-4 mb-0;
}
.el-drawer__body {
@apply p-0;
}
/* 状态标签样式 */
.el-tag {
@apply !rounded-button;
}
/* 表格行悬停效果 */
.el-table__body tr:hover > td {
@apply bg-blue-50;
}
/* 统计卡片悬停效果 */
.hover-shadow-md {
transition: box-shadow 0.3s ease;
}
.hover-shadow-md:hover {
box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
</style>
