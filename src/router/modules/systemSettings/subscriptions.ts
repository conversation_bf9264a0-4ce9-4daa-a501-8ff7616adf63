import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/subscriptionCenter',
  component: Layout,
  name: '/systemSettings/subscriptionCenter',
  meta: {
    title: '订阅中心',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/subscriptionCenter/mySubscriptions',
      name: '/systemSettings/subscriptionCenter/mySubscriptions',
      component: () => import('@/views/hegui/systemSettings/subscriptionCenter/mySubscriptions/index.vue'),
      meta: {
        title: '我的订阅',
      },
      children: [
      ],
    },
    {
      path: '/systemSettings/subscriptionCenter/packagePlan',
      name: '/systemSettings/subscriptionCenter/packagePlan',
      component: () => import('@/views/hegui/systemSettings/subscriptionCenter/packagePlan/index.vue'),
      meta: {
        title: '套餐与方案',
      },
      children: [
        // 套餐与方案详情
        {
          path: '/systemSettings/subscriptionCenter/packagePlan/detail',
          name: '/systemSettings/subscriptionCenter/packagePlan/detail',
          component: () => import('@/views/hegui/systemSettings/subscriptionCenter/packagePlan/detail.vue'),
          meta: {
            title: '套餐与方案详情',
          },
        },
      ],
    },
    {
      path: '/systemSettings/subscriptionCenter/order',
      name: '/systemSettings/subscriptionCenter/order',
      component: () => import('@/views/hegui/systemSettings/subscriptionCenter/order/index.vue'),
      meta: {
        title: '订单管理',
      },
      children: [
      ],
    },
  ],
}

export default routes
