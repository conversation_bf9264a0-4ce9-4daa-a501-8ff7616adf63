<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'
import dictApi from '@/api/modules/system/dict'
import { disablePastDates } from '@/utils/dateUtils'

const _tabsList = ref([
  {
    id: 1,
    name: '基本信息',
  },
  {
    id: 2,
    name: '关联信息',
  },
  {
    id: 3,
    name: '风险分析',
  },
])

const _activeName = ref(1)
const router = useRouter()
const route = useRoute()
const formRef = ref()

// 表单数据
const form = ref({
  id: '',
  caseName: '',
  caseCode: '',
  areaType: '',
  caseSource: '',
  level: '',
  summary: '',
  keyword: '',
  backgroundDesc: '',
  occurDate: '',
  mediaUrl: '',
  violation: '',
  riskAnalysis: '',
  preventionControl: '',
  learningPoints: '',
  trainingUrl: '',
  relatedRegulations: '',
  relatedCases: '',
  createdBy: 'admin',
})

// 表单验证规则
const formRules = ref({
  caseName: [{
    required: true,
    message: '请输入案例名称',
    trigger: 'blur',
  }],
  caseCode: [{
    required: true,
    message: '请输入案例编号',
    trigger: 'blur',
  }],
  areaType: [{
    required: true,
    message: '请选择领域类型',
    trigger: 'change',
  }],
  caseSource: [{
    required: true,
    message: '请选择案例来源',
    trigger: 'change',
  }],
  level: [{
    required: true,
    message: '请选择风险等级',
    trigger: 'change',
  }],
})

// 是否编辑模式
const isEdit = ref(false)

// 领域类型选项
const areaTypeOptions = [
  { value: 'INDUSTRY_REGULATION', label: '行业监管' },
  { value: 'CORPORATE_GOVERNANCE', label: '公司治理' },
  { value: 'BUSINESS_OPERATIONS', label: '业务运营' },
  { value: 'FINANCE_TAXATION', label: '财务税务' },
]

// 案例来源选项
const caseSourceOptions = [
  { value: 'REGULATORY_PENALTIES', label: '监管处罚' },
  { value: 'JUDICIAL_PRECEDENTS', label: '司法判例' },
  { value: 'INDUSTRY', label: '行业案例' },
  { value: 'INTERNAL', label: '内部案例' },
]

// 风险等级选项
const levelOptions = [
  { value: 'GENERAL', label: '一般风险' },
  { value: 'MAJOR', label: '重大风险' },
  { value: 'TYPICAL', label: '典型风险' },
  { value: 'SAFE', label: '安全' },
]

// 获取案例详情（编辑模式）
async function getCaseDetail() {
  try {
    const id = route.query.id
    if (!id) {
      return
    }

    isEdit.value = true
    const response = await systemApi.caseSystem({ id }, 'info')
    if (response) {
      form.value = { ...response }
    }
  }
  catch (error) {
    ElMessage.error('获取案例详情失败')
  }
}

// 保存案例
async function saveCase() {
  try {
    await formRef.value.validate()
    const key = isEdit.value ? 'update' : 'create'
    const params = isEdit.value ? { ...form.value, id: route.query.id } : form.value

    await systemApi.caseSystem(params, key)
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    router.push({ path: '/database/cases' })
  }
  catch (error) {
    ElMessage.error('保存失败')
  }
}

// 取消操作
function cancel() {
  router.back()
}

// 获取案例编号
async function getCaseCode() {
  try {
    const response = await dictApi.getCode('COMPLIANCE_CASE')
    if (response) {
      form.value.caseCode = response
    }
  }
  catch (error) {
    ElMessage.error('获取案例编号失败')
  }
}

// 页面初始化
onMounted(() => {
  getCaseDetail()
  // 新增模式下自动获取案例编号
  if (!route.query.id) {
    getCaseCode()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="aic flex">
            <div class="f-20 mr-16 fw-600">
              {{ isEdit ? '编辑合规案例' : '新增合规案例' }}
            </div>
          </div>
          <div>
            <div class="aic flex" style="justify-content: flex-end;">
              <el-button class="ml-16" @click="cancel">
                <span class="">取消</span>
              </el-button>
              <el-button type="primary" @click="saveCase">
                <span class="">{{ isEdit ? '更新' : '保存' }}</span>
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </page-header>
    <el-form ref="formRef" :rules="formRules" :model="form" label-width="90px" label-position="top">
      <PageMain style="height: calc(100vh - 182px);overflow-y: auto;background-color: transparent;">
        <div class="flex">
          <div class="flex-1" style="flex: 1.5;overflow-y: auto;">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <el-form-item label="案例名称:" prop="caseName">
                <el-input v-model="form.caseName" placeholder="请输入案例名称" />
              </el-form-item>
              <el-row>
                <el-col :span="8" class="pr-10">
                  <el-form-item label="案例编号:" prop="caseCode">
                    <el-input v-model="form.caseCode" placeholder="案例编号将自动生成" :disabled="!isEdit" />
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="pl-10">
                  <el-form-item label="领域类型:" prop="areaType">
                    <el-select v-model="form.areaType" placeholder="请选择领域类型" style="width: 100%;">
                      <el-option
                        v-for="item in areaTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="pl-10">
                  <el-form-item label="案例来源:" prop="caseSource">
                    <el-select v-model="form.caseSource" placeholder="请选择案例来源" style="width: 100%;">
                      <el-option
                        v-for="item in caseSourceOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8" class="pr-10">
                  <el-form-item label="风险等级:" prop="level">
                    <el-select v-model="form.level" placeholder="请选择风险等级" style="width: 100%;">
                      <el-option
                        v-for="item in levelOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="pl-10">
                  <el-form-item label="发生时间:" prop="occurDate">
                    <el-date-picker
                      v-model="form.occurDate"
                      type="date"
                      placeholder="请选择发生时间"
                      style="width: 100%;"
                      value-format="YYYY-MM-DD"
                      :disabled-date="disablePastDates"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="pl-10">
                  <el-form-item label="关键词:" prop="keyword">
                    <el-input v-model="form.keyword" placeholder="请输入关键词，多个用逗号分隔" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
            <el-card class="mt-24">
              <template #header>
                <div class="card-header">
                  <span>案例内容</span>
                </div>
              </template>
              <el-form-item label="案例概述:" prop="summary">
                <el-input v-model="form.summary" :rows="4" type="textarea" placeholder="请输入案例概述" />
              </el-form-item>
              <el-form-item label="背景描述:" prop="backgroundDesc">
                <el-input v-model="form.backgroundDesc" :rows="6" type="textarea" placeholder="请输入背景描述" />
              </el-form-item>
              <el-form-item label="多媒体资料:" prop="mediaUrl">
                <el-input v-model="form.mediaUrl" placeholder="请输入多媒体资料链接" />
              </el-form-item>
            </el-card>
            <el-card class="mt-24">
              <template #header>
                <div class="card-header">
                  <span>法律分析</span>
                </div>
              </template>
              <el-form-item label="违规点分析:" prop="violation">
                <el-input v-model="form.violation" :rows="4" type="textarea" placeholder="请输入违规点分析" />
              </el-form-item>
            </el-card>
            <el-card class="mt-24">
              <template #header>
                <div class="card-header">
                  <span>经验教训</span>
                </div>
              </template>
              <el-form-item label="风险分析：" prop="riskAnalysis">
                <el-input v-model="form.riskAnalysis" :rows="4" type="textarea" placeholder="请输入风险分析" />
              </el-form-item>
              <el-form-item label="防控建议：" prop="preventionControl">
                <el-input v-model="form.preventionControl" :rows="4" type="textarea" placeholder="请输入防控建议" />
              </el-form-item>
              <el-form-item label="学习要点：" prop="learningPoints">
                <el-input v-model="form.learningPoints" :rows="4" type="textarea" placeholder="请输入学习要点" />
              </el-form-item>
              <el-form-item label="培训资料：" prop="trainingUrl">
                <el-input v-model="form.trainingUrl" placeholder="请输入培训资料链接" />
              </el-form-item>
            </el-card>
            <!-- <el-card class="mt-24">
              <template #header>
                <div class="card-header">
                  <span>关联信息</span>
                </div>
              </template>
              <el-form-item label="相关法规:" prop="relatedRegulations">
                <el-input v-model="form.relatedRegulations" placeholder="请输入相关法规" />
              </el-form-item>
              <el-form-item label="相关案例:" prop="relatedCases">
                <el-input v-model="form.relatedCases" placeholder="请输入相关案例" />
              </el-form-item>
            </el-card> -->
          </div>
        </div>
      </PageMain>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .css-xgys {
    display: flex;
    place-content: center center;
    width: fit-content;
    padding: 0 7px 0 8px;
    font-size: 12px;
    font-weight: 400;
    color: #0958d9;
    background: #e6f4ff;
    border: 1px solid #91caff;
    border-radius: 4px;
  }
</style>
