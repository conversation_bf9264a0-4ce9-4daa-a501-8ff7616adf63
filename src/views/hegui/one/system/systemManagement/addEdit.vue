<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import useUserStore from '@/store/modules/user'

import systemApi from '@/api/complianceApi/one/systemManagement.ts'
import dictApi from '@/api/modules/system/dict'
import LawsPop from '@/components/lawsPop/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import RichText from '@/components/richText/index.vue'
import { disablePastDates } from '@/utils/dateUtils'

const loading = ref(false)
const btnLoading = ref(false)
const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const _userStore = useUserStore()
// 是否是编辑模式
const isEdit = computed(() => !!route.query.id)

const AttachmentType = ref('OTHER')
// 表单数据
const formData = reactive({
  id: null,
  title: '',
  regulationCode: '',
  version: '',
  regulationType: '',
  summary: '',
  content: '',
  department: '',
  effectiveDate: '',
  expireDate: '',
  categoryId: '',
  changeLog: '',
  attachments: [] as any[],
  regulationIds: [] as string[],
  status: 'DRAFT',
})

// 日期验证函数
function validateEffectiveDate(rule: any, value: any, callback: any) {
  if (!value) {
    callback(new Error('请选择生效日期'))
    return
  }

  const today = new Date()
  today.setHours(0, 0, 0, 0) // 设置为当天的开始时间
  const selectedDate = new Date(value)

  if (selectedDate < today) {
    callback(new Error('生效日期不能小于当前日期'))
    return
  }

  callback()
}

function validateExpireDate(rule: any, value: any, callback: any) {
  if (!value) {
    callback() // 失效日期不是必填项
    return
  }

  const today = new Date()
  today.setHours(0, 0, 0, 0) // 设置为当天的开始时间
  const selectedDate = new Date(value)

  if (selectedDate < today) {
    callback(new Error('失效日期不能小于当前日期'))
    return
  }

  // 如果生效日期已选择，失效日期不能小于生效日期
  if (formData.effectiveDate) {
    const effectiveDate = new Date(formData.effectiveDate)
    if (selectedDate <= effectiveDate) {
      callback(new Error('失效日期必须大于生效日期'))
      return
    }
  }

  callback()
}
const regulationStatus = ref({
  DRAFT: '草稿',
  PUBLISHED: '发布',
  MODIFYING: '修改',
  REVIEWING: '代审核中',
} as Record<string, string>)
// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入制度名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
  ],
  regulationCode: [
    { required: false, message: '制度编号自动生成', trigger: 'blur' },
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
  ],
  regulationType: [
    { required: true, message: '请选择制度类型', trigger: 'change' },
  ],
  summary: [
    { required: true, message: '请输入制度摘要', trigger: 'blur' },
  ],
  content: [
    { required: true, message: '请输入制度内容', trigger: 'blur' },
  ],
  department: [
    { required: true, message: '请输入所属部门', trigger: 'blur' },
  ],
  effectiveDate: [
    { required: true, validator: validateEffectiveDate, trigger: 'change' },
  ],
  expireDate: [
    { validator: validateExpireDate, trigger: 'change' },
  ],
  categoryId: [
    { required: false, message: '请选择制度分类', trigger: 'change' },
  ],
  changeLog: [
    { required: true, message: '请输入变更说明', trigger: 'blur' },
  ],
})

// 制度类型选项
const regulationTypeOptions = [
  { label: '规章制度', value: 'REGULATION' },
  { label: '管理办法', value: 'MEASURES' },
  { label: '行为准则', value: 'CONDUCT' },
]

// 分类树数据
const categoryTree = ref([] as any[])
// 选中的分类
const selectedCategory = ref(null as any)

// 关联制度弹窗
const showLawsPop = ref(false)
// 选中的关联制度
const selectedRegulations = ref([] as string[])
// 选中的关联制度完整数据
const selectedRegulationItems = ref([] as any[])

// 初始化
onMounted(() => {
  getCategoryTree()

  // 如果是编辑模式，获取制度详情
  if (isEdit.value) {
    getRegulationDetail()
  }
  else {
    // 新增模式下自动获取制度编号
    getRegulationCode()
  }
})

// 获取制度编号
function getRegulationCode() {
  dictApi.getCode('ENTERPRISE').then((res: any) => {
    if (res) {
      formData.regulationCode = res
    }
  }).catch((error: any) => {
    // console.error('获取制度编号失败:', error)
    // 错误提示已在响应拦截器中统一处理
  })
}

// 获取制度详情
function getRegulationDetail() {
  loading.value = true
  const id = route.query.id
  if (!id) {
    return
  }

  systemApi.system({ id }, 'info').then((res: any) => {
    if (res) {
      const data = res
      // 填充表单数据
      Object.keys(formData).forEach((key) => {
        if (data[key] !== undefined) {
          (formData as any)[key] = (data as any)[key]
        }
      })
      loading.value = false
      // 处理附件
      if (data.attachments && data.attachments.length > 0) {
        formData.attachments = data.attachments
      }

      // 处理关联制度
      if (data.regulationIds && data.regulationIds.length > 0) {
        selectedRegulations.value = data.regulationIds
        selectedRegulationItems.value = data.lawsRegulationList
        formData.regulationIds = data.regulationIds
      }

      // 处理分类
      if (data.categoryId) {
        selectedCategory.value = data.categoryId
        formData.categoryId = data.categoryId
      }
    }
  }).catch((error: any) => {
    // console.error('获取制度详情失败:', error)
    ElMessage.error('获取制度详情失败')
  })
}

// 获取分类树
function getCategoryTree() {
  systemApi.systemCategories({ }).then((res: any) => {
    if (res) {
      categoryTree.value = res.data || res
    }
  }).catch((error: any) => {
    // console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  })
}

// 打开关联制度选择弹窗
function openLawsPop() {
  showLawsPop.value = true
}

// 处理关联制度选择确认
function handleLawsConfirm(selectedIds: string[], selectedItems: any[]) {
  selectedRegulations.value = selectedIds
  selectedRegulationItems.value = selectedItems
  formData.regulationIds = selectedIds
}

// 移除关联制度
function removeRegulation(id: string) {
  const index = selectedRegulations.value.indexOf(id)
  if (index > -1) {
    selectedRegulations.value.splice(index, 1)
    selectedRegulationItems.value.splice(index, 1)
    formData.regulationIds = selectedRegulations.value
  }
}

// 选择分类
function handleCategoryChange(value: any) {
  formData.categoryId = value
}

// 处理生效日期变化
function handleEffectiveDateChange() {
  // 当生效日期变化时，重新验证失效日期
  if (formData.expireDate) {
    formRef.value?.validateField('expireDate')
  }
}

// 处理富文本内容变化
function handleContentChange(content: string) {
  formData.content = content
}

// 附件上传成功回调
function handleAttachmentUploadSuccess(files: any[]) {
  // formData.attachments 已经通过 v-model 自动更新
}

// 附件上传失败回调
function handleAttachmentUploadError(error: any) {
  // console.error('附件上传失败:', error)
  ElMessage.error('附件上传失败')
}

// 智能分析
function handleZdAnalysis() {
  // 检查是否有上传的附件
  if (!formData.attachments || formData.attachments.length === 0) {
    ElMessage.warning('请先上传附件')
    return
  }

  // 获取附件的filePath
  const filePath = formData.attachments[0].filePath
  if (!filePath) {
    ElMessage.warning('附件路径不存在')
    return
  }

  btnLoading.value = true
  systemApi.zdAnalysis(filePath).then((res: any) => {
    ElMessage.success('智能分析完成')
    res.status = formData.status
    res.attachments = formData.attachments
    Object.assign(formData, res)
    // 这里可以根据需要处理分析结果
  }).catch((error: any) => {
    // console.error('智能分析失败:', error)
    ElMessage.error('智能分析失败，请稍后重试')
  }).finally(() => {
    btnLoading.value = false
  })
}

// 保存草稿
function saveDraft() {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }
    if (formData.status !== 'DRAFT') {
      ElMessage.success(`${regulationStatus.value[formData.status]}状态不能保存为草稿`)
      return
    }
    const params = { ...formData, status: 'DRAFT' }
    if (isEdit.value) {
      // 更新 - 保留id
      systemApi.system(params, 'update').then(() => {
        ElMessage.success('保存草稿成功')
        router.push('/one/systemManagement/index')
      }).catch((error: any) => {
        // console.error('保存草稿失败:', error)
        ElMessage.error('保存草稿失败')
      })
    }
    else {
      // 新增 - 不传id
      const { ...createParams } = params
      systemApi.system({ ...createParams }, 'create').then(() => {
        ElMessage.success('保存草稿成功')
        router.push('/one/systemManagement/index')
      }).catch((error: any) => {
        // console.error('保存草稿失败:', error)
        ElMessage.error(error?.response?.data?.title || '保存草稿失败')
      })
    }
  })
}

// 提交发布
function submitPublish(status: any) {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }

    ElMessageBox.confirm('确定要保存该制度吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const params = { ...formData, status }
      if (isEdit.value) {
        // 更新并发布 - 保留id
        systemApi.system(params, 'update').then(() => {
          ElMessage.success('更新成功')
          router.push('/one/systemManagement/index')
          // systemApi.system({ id: formData.id }, 'publish').then(() => {
          //   ElMessage.success('发布成功')
          //   router.push('/one/systemManagement/index')
          // })
        }).catch((error: any) => {
          // console.error('更新失败:', error)
          // 错误提示已在响应拦截器中统一处理
        })
      }
      else {
        // 新增并发布 - 不传id，直接创建为已发布状态
        const { ...createParams } = params
        systemApi.system({ ...createParams }, 'create').then(() => {
          ElMessage.success('保存成功')
          router.push('/one/systemManagement/index')
        }).catch((error: any) => {
          // console.error('发布失败:', error)
          // 错误提示已在响应拦截器中统一处理
        })
      }
    }).catch(() => {})
  })
}

// 预览
function previewRegulation() {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }

    // 这里可以实现预览功能，例如打开一个预览对话框
    ElMessage.info('预览功能待实现')
  })
}

// 取消
function cancel() {
  ElMessageBox.confirm('确定要取消编辑吗？未保存的内容将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.push('/one/systemManagement/index')
  }).catch(() => {})
}
</script>

<template>
  <div v-loading="loading" class="system-management">
    <page-header>
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ isEdit ? '修改制度' : '新增制度' }}
            </h1>
          </div>
          <div v-if="formData.status !== 'PUBLISHED'" class="flex space-x-3">
            <el-button v-debounce="3000" @click="cancel">
              取消
            </el-button>
            <el-button v-debounce="3000" :disabled="formData.status !== 'DRAFT'" type="primary" plain @click="saveDraft">
              保存草稿
            </el-button>
            <el-button v-debounce="2000" type="primary" :disabled="formData.status !== 'DRAFT' && formData.status !== 'MODIFYING'" @click="submitPublish('REVIEWING')">
              {{ '保存制度' }}
            </el-button>
            <!-- <el-button v-if="formData.status === 'MODIFYING'" v-debounce="2000" type="primary" @click="submitPublish('REVIEWING')">
              {{ '修改制度' }}
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <page-main>
      <div class="card p-16">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          status-icon
        >
          <el-form-item label="附件">
            <div class="w-full flex justify-end">
              <el-button v-debounce="3000" v-loading="btnLoading" type="success" @click="handleZdAnalysis">
                智能分析
              </el-button>
            </div>
            <UploadMbb
              v-model="formData.attachments"
              :max="1"
              :size="10"
              accept=".doc,.docx"
              tip-text="支持doc、docx格式，单个文件大小不超过10MB，最多上传1个文件"
              service-name="whiskerguardregulatoryservice"
              category-name="regulation"
              :use-file-path="true"
              :attachment-type="AttachmentType"
              @upload-success="handleAttachmentUploadSuccess"
              @upload-error="handleAttachmentUploadError"
            />
          </el-form-item>
          <el-form-item label="制度名称" prop="title">
            <el-input v-model="formData.title" placeholder="请输入制度名称" />
          </el-form-item>

          <el-form-item label="制度编号" prop="regulationCode">
            <div style="display: flex; gap: 8px;">
              <el-input v-model="formData.regulationCode" placeholder="制度编号将自动生成" readonly style="flex: 1;" />
              <el-button v-debounce="3000" type="primary" plain @click="getRegulationCode">
                再次生成
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="版本号" prop="version">
            <el-input v-model="formData.version" placeholder="请输入版本号" />
          </el-form-item>

          <el-form-item label="制度类型" prop="regulationType">
            <el-select v-model="formData.regulationType" placeholder="请选择制度类型" style="width: 100%">
              <el-option
                v-for="item in regulationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="制度摘要" prop="summary">
            <el-input v-model="formData.summary" type="textarea" :rows="3" placeholder="请输入制度摘要" />
          </el-form-item>

          <el-form-item label="制度内容" prop="content">
            <RichText :content="formData.content" @change="handleContentChange" />
          </el-form-item>

          <el-form-item label="适用部门" prop="department">
            <!-- <DepartmentTreeSelect
              v-model="formData.department"
              placeholder="请选择部门"
              clearable
            /> -->
            <el-input v-model="formData.department" placeholder="请输入所属部门" />
          </el-form-item>

          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              type="date"
              placeholder="选择生效日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              :disabled-date="disablePastDates"
              @change="handleEffectiveDateChange"
            />
          </el-form-item>

          <el-form-item label="失效日期" prop="expireDate">
            <el-date-picker
              v-model="formData.expireDate"
              type="date"
              placeholder="选择失效日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              :disabled-date="disablePastDates"
            />
          </el-form-item>

          <el-form-item label="制度分类" prop="categoryId">
            <el-cascader
              v-model="selectedCategory"
              :options="categoryTree"
              :props="{
                checkStrictly: true,
                label: 'categoryName',
                value: 'id',
                children: 'children',
                emitPath: false,
              }"
              placeholder="请选择制度分类"
              style="width: 100%"
              clearable
              @change="handleCategoryChange"
            />
          </el-form-item>

          <!-- <el-form-item label="分类名称" prop="categoryName">
            <el-input v-model="formData.categoryName" placeholder="请输入分类名称" />
          </el-form-item> -->

          <el-form-item v-if="!isEdit" label="变更说明" prop="changeLog">
            <el-input v-model="formData.changeLog" type="textarea" :rows="3" placeholder="请输入变更说明" />
          </el-form-item>

          <el-form-item label="关联法规">
            <div class="regulation-selector">
              <!-- 已选择的制度标签 -->
              <div v-if="selectedRegulationItems.length > 0" class="selected-regulations mb-2">
                <el-tag
                  v-for="item in selectedRegulationItems"
                  :key="item.id"
                  closable
                  type="info"
                  class="mb-2 mr-2"
                  @close="removeRegulation(item.id)"
                >
                  {{ item.title }}
                </el-tag>
              </div>

              <!-- 选择按钮 -->
              <el-button v-debounce="3000" type="primary" plain @click="openLawsPop">
                <el-icon><Plus /></el-icon>
                选择关联法规
              </el-button>

              <div v-if="selectedRegulationItems.length === 0" class="mt-2 text-sm text-gray-500">
                暂未选择关联法规
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </page-main>

    <!-- 关联制度选择弹窗 -->
    <LawsPop
      v-model="showLawsPop"
      :selected-values="selectedRegulations"
      @confirm="handleLawsConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
</style>
