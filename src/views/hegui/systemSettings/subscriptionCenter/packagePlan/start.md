---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 10-订单管理服务/统一支付接口

## POST 开始支付

POST /whiskerguardorderservice/union/pay

{@code POST  /union/pay} : Start payment process.

> Body 请求参数

```json
{
  "payWay": 0,
  "orderId": "string",
  "totalAmount": 0,
  "subject": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[PayDTO](#schemapaydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "htmlContent": "",
  "codeUrl": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPayPageResponse](#schemaresponseentitypaypageresponse)|

# 数据模型

<h2 id="tocS_ResponseEntityPayPageResponse">ResponseEntityPayPageResponse</h2>

<a id="schemaresponseentitypaypageresponse"></a>
<a id="schema_ResponseEntityPayPageResponse"></a>
<a id="tocSresponseentitypaypageresponse"></a>
<a id="tocsresponseentitypaypageresponse"></a>

```json
{
  "htmlContent": "string",
  "codeUrl": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|htmlContent|string|false|none||HTML内容(支付宝方式）|
|codeUrl|string|false|none||跳转URL（微信方式）|

<h2 id="tocS_PayDTO">PayDTO</h2>

<a id="schemapaydto"></a>
<a id="schema_PayDTO"></a>
<a id="tocSpaydto"></a>
<a id="tocspaydto"></a>

```json
{
  "payWay": 0,
  "orderId": "string",
  "totalAmount": 0,
  "subject": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|payWay|integer|true|none||支付方式：0-支付宝；1-微信|
|orderId|string|true|none||none|
|totalAmount|number|false|none||none|
|subject|string|false|none||支付标题|

