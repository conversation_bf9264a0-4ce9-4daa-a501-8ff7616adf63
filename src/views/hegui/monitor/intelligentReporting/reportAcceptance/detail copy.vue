<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElAlert,
  ElButton,
  ElCard,
  ElIcon,
  ElLink,
  ElMessage,
  ElProgress,
  ElTabPane,
  ElTable,
  ElTableColumn,
  ElTabs,
  ElTag,
} from 'element-plus'
import intelligentReportingApi from '@/api/report/intelligentReporting'
import { useTaskStore } from '@/store/modules/task'

const route = useRoute()
const router = useRouter()
const taskStore = useTaskStore()
const activeTab = ref('detail')
const loading = ref(false)

const tableData = ref([])
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
})
// 举报详情数据
const violationDetail = ref<any>({})

// 举报对象数据（从详情中解析）
const reportedObjects = ref<any[]>([])

// 弹窗相关变量
const showRiskDialog = ref(false)
const tempRiskLevel = ref(0)
const selectedRiskLevel = ref(0)
const reportId = ref('')
const problemInvestigateId = ref('')

// 违规类型映射
const violationTypeMap = {
  FINANCIAL_VIOLATION: '财务违规',
  BUSINESS_ETHIC: '商业道德',
  INFORMATION_SECURITY: '信息安全',
  HUMAN_RESOURCE: '人力资源',
}

// 状态映射
const statusMap = {
  PENDING: '待处理',
  REPLIED: '已回复',
  CLOSED: '已关闭',
}

// 风险等级映射
const levelMap = {
  LOW: '低',
  MIDDLE: '中',
  HIGH: '高',
}

// 获取详情数据
async function getDetailData() {
  loading.value = true
  const id = route.query.id
  reportId.value = id
  // if (!id) {
  //   ElMessage.error('缺少举报ID参数')
  //   return
  // }
  // 获取举报详情信息
  try {
    const response = await intelligentReportingApi.violation({}, { id }, 'info')
    if (response) {
      violationDetail.value = response
      // 解析举报对象信息（从objectMsg字段）
      if (response.objectMsg) {
        try {
          const objectData = JSON.parse(response.objectMsg)
          reportedObjects.value = Array.isArray(objectData) ? objectData : [objectData]
        }
        catch (e) {
          // 如果解析失败，使用默认格式
          reportedObjects.value = [{
            type: '未知',
            name: response.objectMsg,
            position: '-',
            description: '-',
          }]
        }
      }
    }
  }
  catch (error) {
    console.error('获取举报详情失败:', error)
    // 错误提示已在响应拦截器中统一处理
  }

  // 获取处理记录列表
  try {
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      detailId: id,
      // ...searchForm
    }
    const handlResponse = await intelligentReportingApi.handling(params, null)
    if (handlResponse && handlResponse.content) {
      tableData.value = handlResponse.content || []
      pagination.value.total = handlResponse.totalElements || 0
      console.log('handlResponse', tableData.value)
    }
  }
  catch (error) {
    console.error('获取处理记录失败:', error)
    ElMessage.error('获取处理记录失败')
  }

  loading.value = false
}

// 获取违规类型显示文本
function getViolationTypeText(type: string) {
  return violationTypeMap[type as keyof typeof violationTypeMap] || type
}

// 获取状态显示文本
function getStatusText(status: string) {
  return statusMap[status as keyof typeof statusMap] || status
}

// 获取风险等级显示文本
function getLevelText(level: string) {
  return levelMap[level as keyof typeof levelMap] || level
}

// 获取状态标签类型
function getStatusTagType(status: string) {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'REPLIED': return 'info'
    case 'CLOSED': return 'success'
    default: return 'info'
  }
}

// 获取风险等级标签类型
function getLevelTagType(level: string) {
  switch (level) {
    case 'LOW': return 'success'
    case 'MIDDLE': return 'warning'
    case 'HIGH': return 'danger'
    default: return 'info'
  }
}

// 下载文件
function downloadFile(file: any) {
  if (file.filePath) {
    window.open(file.filePath, '_blank')
  }
  else {
    ElMessage.warning('文件路径不存在')
  }
}

// 查看详情
function handleView(row: any) {
  ElMessage.info(`查看详情: ${row.id}`)
}

// 处理记录
function handleProcess(row: any) {
  ElMessage.info(`处理记录: ${row.id}`)
}

// 开始调查
function handleStartInvestigation() {
  const reportId = route.query.id
  router.push({
    path: '/respond/violationIssues/taskManagement/addEdit',
    query: {
      type: 'report',
      reportId,
    },
  })
}

// 确认处理
function submitReport() {
  if (violationDetail.value.violationDealDTO) {
    ElMessage.warning('已创建调查任务，不能重复创建')
    return
  }

  tempRiskLevel.value = 0
  showRiskDialog.value = true
}

// 确认选择风险等级
function confirmRiskLevel() {
  selectedRiskLevel.value = tempRiskLevel.value
  showRiskDialog.value = false
  handleSubmitWithLevel()
}

// 取消选择风险等级
function cancelRiskLevel() {
  showRiskDialog.value = false
}

// 处理提交逻辑
function handleSubmitWithLevel() {
  // 构建提交参数
  const levelMap = ['LOW', 'MIDDLE', 'HIGH']
  const params = {
    detailId: route.query.id,
    level: levelMap[selectedRiskLevel.value] || 'LOW',
    problemInvestigateId: problemInvestigateId.value || '',
  }

  loading.value = true

  intelligentReportingApi.createViolation(params)
    .then(() => {
      ElMessage.success('提交成功')
      // 刷新页面数据
      setTimeout(() => {
        getDetailData()
      }, 1500)
    })
    .catch(() => {
      ElMessage.error('提交失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 导出
function _handleExport() {
  ElMessage.info(`导出功能待开发，当前任务ID`)
}

// 返回
function handleBack() {
  // 可以使用 router.back() 或跳转到列表页
  window.history.back()
}

// 转发举报
function handleForward() {
  ElMessage.info('转发举报功能待开发')
}

// 暂停处理
function handlePause() {
  ElMessage.info('暂停处理功能待开发')
}

// 关闭举报
function handleClose() {
  ElMessage.info('关闭举报功能待开发')
}

// 回复举报人
function handleReply() {
  ElMessage.info('回复举报人功能待开发')
}

// 组件挂载时获取数据
onMounted(() => {
  getDetailData()
})

// 监听 Pinia store 中的任务ID变化
watch(() => taskStore.currentTaskId, (newTaskId) => {
  if (newTaskId) {
    ElMessage.success(`调查任务已创建，任务ID: ${newTaskId}`)
    problemInvestigateId.value = newTaskId
    // 刷新页面数据以显示最新状态
    getDetailData()
  }
}, { immediate: true })

// 组件卸载时清空任务ID
onUnmounted(() => {
  taskStore.clearTaskId()
})
</script>

<template>
  <div class="absolute-container">
    <div style="height: 100%;overflow-y: auto;">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex">
            <div class="mt-4 flex items-center justify-between">
              <h1 class="text-xl c-[#000000] font-bold">
                举报详情 #{{ violationDetail.violationCode || '-' }}
              </h1>
              <ElTag :type="getStatusTagType(violationDetail.status)" class="ml-4" size="small">
                {{ getStatusText(violationDetail.status) }}
              </ElTag>
            </div>
            <div class="flex items-center space-x-2">
              <ElButton v-auth="['reportAcceptance/detail/startInvestigation']" type="primary" class="rounded-button whitespace-nowrap" @click="submitReport">
                确认处理
              </ElButton>
              <ElButton v-auth="['reportAcceptance/detail/startInvestigation']" type="primary" class="rounded-button whitespace-nowrap" @click="handleStartInvestigation">
                开始调查
              </ElButton>
              <ElButton v-auth="['reportAcceptance/detail/reback']" plain class="rounded-button whitespace-nowrap" @click="handleBack">
                返回
              </ElButton>
            </div>
          </div>
        </template>
      </page-header>
      <PageMain style="background-color: transparent;">
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <ElCard class="">
              <template #header>
                <div class="f-16 fw-600">
                  举报基本信息
                </div>
              </template>
              <div v-loading="loading" class="grid grid-cols-2 gap-4">
                <div class="flex">
                  <span class="w-24 text-gray-500">举报编号：</span>
                  <span>{{ violationDetail.violationCode || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">举报标题：</span>
                  <span>{{ violationDetail.title || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">举报类型：</span>
                  <span>{{ getViolationTypeText(violationDetail.violationType) }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">风险等级：</span>
                  <ElTag v-if="violationDetail.violationDealDTO?.level" :type="getLevelTagType(violationDetail.violationDealDTO?.level)" size="small">
                    {{ getLevelText(violationDetail.violationDealDTO?.level) }}
                  </ElTag>
                  <span v-else>-</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">接收时间：</span>
                  <span>{{ violationDetail.createdAt || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">举报渠道：</span>
                  <span>内部举报系统</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">举报人：</span>
                  <span>{{ violationDetail.isAnonymous ? '匿名' : (violationDetail.createdBy || '-') }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">联系方式：</span>
                  <span>{{ violationDetail.contactWay || '未提供' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">状态：</span>
                  <ElTag :type="getStatusTagType(violationDetail.status)" size="small">
                    {{ getStatusText(violationDetail.status) }}
                  </ElTag>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">处理部门：</span>
                  <span>合规部</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">处理人：</span>
                  <span>{{ violationDetail.updatedBy || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">更新时间：</span>
                  <span>{{ violationDetail.updatedAt || '-' }}</span>
                </div>
              </div>
            </ElCard>
            <!-- 举报内容 -->
            <ElCard shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  举报内容
                </div>
              </template>
              <div v-loading="loading" class="max-w-none prose">
                <div v-if="violationDetail.detail" v-html="violationDetail.detail.replace(/\n/g, '<br>')" />
                <div v-else class="text-gray-500">
                  暂无举报内容详情
                </div>
              </div>
            </ElCard>
            <!-- 详情 -->
            <ElCard class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  详情
                </div>
              </template>
              <ElTable
                v-loading="loading"
                :data="tableData"
                style="width: 100%;"
                highlight-current-row
                border
              >
                <ElTableColumn type="selection" width="55" />
                <ElTableColumn prop="id" label="举报编号" sortable width="140">
                  <template #default="{ row }">
                    {{ row.violationDetail?.reportNumber || row.id }}
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="title" label="举报标题" sortable min-width="180">
                  <template #default="{ row }">
                    {{ row.violationDetail?.title || '无标题' }}
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="type" label="举报类型" sortable width="120">
                  <template #default="{ row }">
                    <ElTag size="small">
                      {{ row.violationDetail?.type || '未知类型' }}
                    </ElTag>
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="level" label="优先级" sortable width="100">
                  <template #default="{ row }">
                    <ElTag
                      size="small"
                      :type="getLevelTagType(row.level)"
                    >
                      {{ getLevelText(row.level) }}
                    </ElTag>
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="status" label="状态" sortable width="120">
                  <template #default="{ row }">
                    <ElTag
                      size="small"
                      type="primary"
                    >
                      {{ row.violationDetail?.status || '未知状态' }}
                    </ElTag>
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="createdAt" label="接收时间" sortable width="160">
                  <template #default="{ row }">
                    {{ row.createdAt || '-' }}
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="updatedBy" label="处理人" sortable width="120" />
                <ElTableColumn prop="updatedAt" label="更新时间" sortable width="160">
                  <template #default="{ row }">
                    {{ row.updatedAt || '-' }}
                  </template>
                </ElTableColumn>
                <ElTableColumn label="操作" width="180" fixed="right">
                  <template #default="{ row }">
                    <div class="flex space-x-2">
                      <ElButton v-auth="['reportAcceptance/detail/review']" size="small" class="!rounded-button whitespace-nowrap" @click="handleView(row)">
                        查看
                      </ElButton>
                      <ElButton v-auth="['reportAcceptance/detail/dispose']" type="primary" size="small" class="!rounded-button whitespace-nowrap" @click="handleProcess(row)">
                        处理
                      </ElButton>
                      <el-dropdown>
                        <ElButton v-auth="['reportAcceptance/detail/more']" size="small" class="!rounded-button whitespace-nowrap">
                          更多<ElIcon class="ml-1">
                            <ElIconArrowDown />
                          </ElIcon>
                        </ElButton>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item>分配</el-dropdown-item>
                            <el-dropdown-item>转交</el-dropdown-item>
                            <el-dropdown-item divided>
                              关闭
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </template>
                </ElTableColumn>
              </ElTable>
              <ElTabs v-if="false" v-model="activeTab">
                <ElTabPane label="举报详情" name="detail">
                  <!-- 举报对象 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-base font-semibold">
                      举报对象
                    </h3>
                    <ElTable :data="reportedObjects" style="width: 100%;">
                      <ElTableColumn prop="type" label="对象类型" width="120" />
                      <ElTableColumn prop="name" label="名称" width="120" />
                      <ElTableColumn prop="position" label="职位/角色" width="150" />
                      <ElTableColumn prop="description" label="涉事描述" />
                    </ElTable>
                  </div>

                  <!-- 事件描述 -->
                  <div class="mb-8">
                    <h3 class="mb-4 text-base font-semibold">
                      事件描述
                    </h3>
                    <div v-loading="loading" class="max-w-none prose">
                      <div v-if="violationDetail.detail" v-html="violationDetail.detail.replace(/\n/g, '<br>')" />
                      <div v-else class="text-gray-500">
                        暂无详细事件描述
                      </div>
                    </div>
                  </div>

                  <!-- 初步分析 -->
                  <div>
                    <h3 class="mb-4 text-base font-semibold">
                      初步分析
                    </h3>
                    <ElAlert type="info" :closable="false" class="mb-4">
                      <template #title>
                        <div class="flex items-center">
                          <ElIcon class="mr-2">
                            <i class="fas fa-robot" />
                          </ElIcon>
                          <span>AI 辅助分析</span>
                        </div>
                      </template>
                      <div class="mt-1">
                        <p>根据举报内容和历史数据分析，该举报可信度较高（85%）。举报中提到的三笔异常报销均存在明显疑点，建议重点调查。</p>
                        <p class="mt-2">
                          系统匹配到3个相似案例，均为市场部人员违规报销问题，处理结果包括追回款项、警告处分和流程整改。
                        </p>
                      </div>
                    </ElAlert>
                    <div class="grid grid-cols-3 gap-4">
                      <ElCard shadow="never">
                        <div class="flex items-center">
                          <ElIcon class="mr-3 text-orange-500">
                            <i class="fas fa-exclamation-triangle" />
                          </ElIcon>
                          <div>
                            <h4 class="text-sm text-gray-900 font-medium">
                              风险评分
                            </h4>
                            <p class="text-sm text-gray-500">
                              {{ violationDetail.violationDealDTO?.level ? `${getLevelText(violationDetail.violationDealDTO.level)}风险` : '未评估' }}
                            </p>
                          </div>
                        </div>
                      </ElCard>
                      <ElCard shadow="never">
                        <div class="flex items-center">
                          <ElIcon class="mr-3 text-blue-500">
                            <i class="fas fa-project-diagram" />
                          </ElIcon>
                          <div>
                            <h4 class="text-sm text-gray-900 font-medium">
                              涉及部门
                            </h4>
                            <p class="text-sm text-gray-500">
                              市场部、财务部
                            </p>
                          </div>
                        </div>
                      </ElCard>
                      <ElCard shadow="never">
                        <div class="flex items-center">
                          <ElIcon class="mr-3 text-purple-500">
                            <i class="fas fa-history" />
                          </ElIcon>
                          <div>
                            <h4 class="text-sm text-gray-900 font-medium">
                              处理时限
                            </h4>
                            <p class="text-sm text-gray-500">
                              剩余7天
                            </p>
                          </div>
                        </div>
                      </ElCard>
                    </div>
                  </div>
                </ElTabPane>
                <ElTabPane label="处理记录" name="records">
                  <div class="py-8 text-center text-gray-500">
                    暂无处理记录
                  </div>
                </ElTabPane>
                <ElTabPane label="调查结果" name="results">
                  <div class="py-8 text-center text-gray-500">
                    暂无调查结果
                  </div>
                </ElTabPane>
                <ElTabPane label="证据材料" name="evidence">
                  <div v-if="violationDetail.attachmentList && violationDetail.attachmentList.length > 0">
                    <ElTable :data="violationDetail.attachmentList" style="width: 100%;">
                      <ElTableColumn prop="fileName" label="文件名称" />
                      <ElTableColumn prop="fileType" label="文件类型" width="120" />
                      <ElTableColumn prop="fileSize" label="文件大小" width="120" />
                      <ElTableColumn prop="fileDesc" label="文件描述" />
                      <ElTableColumn prop="uploadedBy" label="上传者" width="120" />
                      <ElTableColumn label="上传时间" width="180">
                        <template #default="{ row }">
                          {{ row.uploadedAt || '-' }}
                        </template>
                      </ElTableColumn>
                      <ElTableColumn label="操作" width="120">
                        <template #default="{ row }">
                          <ElButton type="primary" size="small" @click="downloadFile(row)">
                            下载
                          </ElButton>
                        </template>
                      </ElTableColumn>
                    </ElTable>
                  </div>
                  <div v-else class="py-8 text-center text-gray-500">
                    暂无证据材料
                  </div>
                </ElTabPane>
                <ElTabPane label="操作历史" name="history">
                  <div class="py-8 text-center text-gray-500">
                    暂无操作历史
                  </div>
                </ElTabPane>
              </ElTabs>
            </ElCard>
          </el-col>
          <el-col :span="6">
            <!-- 处理时限 -->
            <ElCard class="">
              <template #header>
                <div class="f-16 fw-600">
                  处理时限
                </div>
              </template>
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-gray-500">
                    剩余处理时间
                  </p>
                  <p class="text-xl font-semibold">
                    7天
                  </p>
                </div>
                <div class="h-20 w-20">
                  <ElProgress type="dashboard" :percentage="70" :width="80" :stroke-width="8" color="#3b82f6">
                    <template #default>
                      <span class="text-sm font-medium">70%</span>
                    </template>
                  </ElProgress>
                </div>
              </div>
              <div class="mt-4">
                <p class="text-sm text-gray-500">
                  截止日期: 2023-05-22
                </p>
              </div>
            </ElCard>
            <!-- 快捷操作 -->
            <ElCard class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-2">
                <div>
                  <ElButton type="primary" class="rounded-button w-full whitespace-nowrap" @click="handleForward">
                    <ElIcon><i class="fas fa-share-square" /></ElIcon>
                    <span>转发举报</span>
                  </ElButton>
                </div>
                <div>
                  <ElButton type="warning" class="rounded-button w-full whitespace-nowrap" @click="handlePause">
                    <ElIcon><i class="fas fa-pause-circle" /></ElIcon>
                    <span>暂停处理</span>
                  </ElButton>
                </div>
                <div>
                  <ElButton type="danger" class="rounded-button w-full whitespace-nowrap" @click="handleClose">
                    <ElIcon><i class="fas fa-times-circle" /></ElIcon>
                    <span>关闭举报</span>
                  </ElButton>
                </div>
                <div>
                  <ElButton type="success" class="rounded-button w-full whitespace-nowrap" @click="handleReply">
                    <ElIcon><i class="fas fa-reply" /></ElIcon>
                    <span>回复举报人</span>
                  </ElButton>
                </div>
              </div>
            </ElCard>
            <!-- 相似案例 -->
            <ElCard class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相似案例
                </div>
              </template>
              <div class="space-y-4">
                <div class="border-b border-gray-200 pb-4">
                  <h4 class="text-sm text-gray-900 font-medium">
                    市场部员工虚报差旅费
                  </h4>
                  <p class="mt-1 text-xs text-gray-500">
                    处理结果: 追回款项，书面警告
                  </p>
                  <ElProgress :percentage="78" :stroke-width="2" :show-text="false" />
                </div>
                <div class="border-b border-gray-200 pb-4">
                  <h4 class="text-sm text-gray-900 font-medium">
                    销售部重复报销招待费
                  </h4>
                  <p class="mt-1 text-xs text-gray-500">
                    处理结果: 追回款项，扣发奖金
                  </p>
                  <ElProgress :percentage="65" :stroke-width="2" :show-text="false" />
                </div>
                <div>
                  <h4 class="text-sm text-gray-900 font-medium">
                    采购部虚开发票报销
                  </h4>
                  <p class="mt-1 text-xs text-gray-500">
                    处理结果: 解除劳动合同
                  </p>
                  <ElProgress :percentage="52" :stroke-width="2" :show-text="false" />
                </div>
              </div>
            </ElCard>
            <!-- 相关规定 -->
            <ElCard class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关规定
                </div>
              </template>
              <ul class="space-y-3">
                <li>
                  <ElLink type="primary" href="#">
                    《公司差旅费管理办法》
                  </ElLink>
                </li>
                <li>
                  <ElLink type="primary" href="#">
                    《员工报销审批流程》
                  </ElLink>
                </li>
                <li>
                  <ElLink type="primary" href="#">
                    《财务违规处理办法》
                  </ElLink>
                </li>
                <li>
                  <ElLink type="primary" href="#">
                    《员工行为规范》
                  </ElLink>
                </li>
              </ul>
            </ElCard>
          </el-col>
        </el-row>
      </PageMain>
    </div>
  </div>

  <!-- 风险等级选择弹窗 -->
  <ElDialog
    v-model="showRiskDialog"
    title="选择风险等级"
    width="400px"
    :close-on-click-modal="false"
  >
    <div class="mb-4">
      <p class="mb-3 text-gray-600">
        请选择风险等级：
      </p>
      <ElSelect
        v-model="tempRiskLevel"
        placeholder="请选择风险等级"
        style="width: 100%;"
      >
        <ElOption label="低风险" :value="0" />
        <ElOption label="中风险" :value="1" />
        <ElOption label="高风险" :value="2" />
      </ElSelect>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="cancelRiskLevel">
          取消
        </ElButton>
        <ElButton type="primary" @click="confirmRiskLevel">
          确定
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
  .breadcrumb-item:not(:last-child)::after {
    margin: 0 8px;
    color: #6b7280;
    content: ">";
  }

  .prose ol {
    padding-left: 1.5em;
    list-style-type: decimal;
  }

  .prose li {
    margin-bottom: 0.5em;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
