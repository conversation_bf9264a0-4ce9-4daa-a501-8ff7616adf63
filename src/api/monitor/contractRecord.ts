import api from '@/api/index'

export default {
  // 分页查询所有审查备案记录
  getFilingsList(params: any) {
    const { page, size, ...filters } = params
    const queryParams = new URLSearchParams()

    if (page !== undefined) { queryParams.append('page', page.toString()) }
    if (size !== undefined) { queryParams.append('size', size.toString()) }

    // 添加筛选条件
    Object.keys(filters).forEach((key) => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        queryParams.append(key, filters[key])
      }
    })

    return api.get(`/whiskerguardcontractservice/api/review/record/filings?${queryParams.toString()}`)
  },

  // 根据ID查询单个审查备案记录
  getFilingDetail(id: number) {
    return api.get(`/whiskerguardcontractservice/api/review/record/filings/${id}`)
  },

  // 审查备案处理
  handleFiling(params: { objectId: number, tenantId: number, reviewType: string }) {
    const queryParams = new URLSearchParams()
    queryParams.append('objectId', params.objectId.toString())
    queryParams.append('tenantId', params.tenantId.toString())
    queryParams.append('reviewType', params.reviewType)

    return api.get(`/whiskerguardcontractservice/api/review/record/filings/handle?${queryParams.toString()}`)
  },
}
