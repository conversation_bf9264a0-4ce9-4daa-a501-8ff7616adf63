<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import {
  ElAvatar,
  ElButton,
  ElCheckbox,
  ElDialog,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElRadio,
  ElRadioGroup,
  ElUpload,
} from 'element-plus'
import {
  Check,
  Minus,
  Picture,
  Plus,
  SuccessFilled,
} from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'
import subscriptionsApi from '@/api/subscriptions'

const _userStore = useUserStore()
const route = useRoute()
const router = useRouter()

// 订单流程数据
const _orderId = ref(`ORD20230615${Math.floor(Math.random() * 10000)}`)

// 支付方式数据
const paymentMethod = ref(1) // 默认微信支付，1=微信，0=支付宝

// 支付状态管理
const showPaymentDialog = ref(false)
const paymentLoading = ref(false)
const qrCodeUrl = ref('')
const paymentHtml = ref('')

// 轮询支付状态管理
const pollingTimer = ref<number | null>(null)
const currentOrderId = ref('')
const maxPollingTime = 2 * 60 * 1000 // 2分钟

// 支付状态管理
const paymentStatus = ref('') // 支付状态：PAID/SUCCESS/FAILED/CANCELLED/PENDING
const isPaymentCompleted = ref(false) // 支付是否完成

// 订单状态管理
const existingOrderId = ref('') // 已存在的订单ID
const isOrderCreated = ref(false) // 订单是否已创建

// 生成二维码图片URL
function generateQRCode(text: string) {
  // 使用在线二维码生成API
  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(text)}`
}

// 套餐数据
const selectedPlan = ref({
  id: 'pro',
  name: '专业版套餐',
  description: '适合50-200人规模的企业，提供完整的功能套件',
  price: 1999,
  userPrice: 50,
  maxUsers: 200,
  features: [
    '用户管理',
    '角色权限控制',
    '数据报表',
    '工作流引擎',
    'API集成',
    '移动端支持',
    '高级安全设置',
    '专属客户支持',
  ],
})

// 功能特性数据
const featuresData = ref<any[]>([])

const userCount = ref(10)
const _billingOptions = ref([
  { value: 'month', label: '月付' },
  { value: 'year', label: '年付 (节省10%)' },
])
const selectedBilling = ref('month')

const additionalServices = ref([
  {
    id: 'consulting',
    name: '专家咨询服务',
    description: '专业顾问提供2小时系统配置指导',
    price: 800,
    selected: false,
  },
  {
    id: 'migration',
    name: '数据迁移服务',
    description: '协助从旧系统迁移数据',
    price: 1500,
    selected: false,
  },
  {
    id: 'training',
    name: '培训服务',
    description: '为团队提供2次系统使用培训',
    price: 1200,
    selected: false,
  },
])

const couponCode = ref('')
const appliedCoupon = ref<{ name: string, discount: number } | null>(null)

// 支付方式
const _paymentMethods = ref([
  {
    value: 'bank',
    label: '对公转账',
    iconClass: 'text-blue-500',
  },
  {
    value: 'online',
    label: '在线支付',
    iconClass: 'text-purple-500',
  },
  {
    value: 'alipay',
    label: '支付宝',
    iconClass: 'text-blue-400',
  },
  {
    value: 'wechat',
    label: '微信支付',
    iconClass: 'text-green-500',
  },
])

const _selectedPayment = ref('bank')
const _fileList = ref([])

// 计算属性
const selectedServices = computed(() => {
  return additionalServices.value.filter(service => service.selected)
})

function calculateTotalPrice() {
  let total = selectedPlan.value.price
  total += selectedPlan.value.userPrice * userCount.value

  selectedServices.value.forEach((service) => {
    total += service.price
  })

  if (selectedBilling.value === 'year') {
    total *= 12
    total *= 0.9 // 年付9折
  }

  if (appliedCoupon.value) {
    total -= appliedCoupon.value.discount
  }

  return total.toFixed(2)
}

function _applyCoupon() {
  if (couponCode.value === 'SUMMER2023') {
    appliedCoupon.value = {
      name: '夏季促销优惠',
      discount: 500,
    }
  }
  else {
    appliedCoupon.value = null
  }
}

function _saveForLater() {
  // 保存订单信息
}

function _confirmCancel() {
  // 确认取消订单
  router.back()
}

async function confirmPayment() {
  // 防止重复点击
  if (paymentLoading.value) {
    return
  }

  try {
    paymentLoading.value = true

    // 创建新订单
    const params = {
      businessType: 'CREATE',
      // price: calculateTotalPrice(),
      price: 0.01,
      licenseSubscription: {
        productCode: selectedPlan.value.id,
      },
    }

    const _res = await subscriptionsApi.createPayOrder(params)

    // 保存订单信息
    existingOrderId.value = _res.payOrderId
    isOrderCreated.value = true

    // 开始支付
    const payParams = {
      orderId: _res.payOrderId,
      payWay: paymentMethod.value,
    }

    const payRes = await subscriptionsApi.startPayment(payParams)

    // 使用 payRes.id 作为轮询的订单ID
    currentOrderId.value = _res.id

    // 根据支付方式处理响应
    if (paymentMethod.value === 1) {
      // 微信支付 - 显示二维码并开始后台轮询
      if (payRes.codeUrl) {
        qrCodeUrl.value = payRes.codeUrl
        paymentHtml.value = ''
        showPaymentDialog.value = true
        // 开始轮询支付状态（不显示进度弹窗）
        startPaymentPolling(false)
      }
    }
    else {
      // 支付宝 - 显示确认弹窗
      if (payRes.htmlContent) {
        paymentHtml.value = payRes.htmlContent
        qrCodeUrl.value = ''
        showPaymentDialog.value = true
      }
    }
  }
  catch (error) {
    console.error('支付处理失败:', error)
    ElMessage.error('支付处理失败，请重试')
  }
  finally {
    paymentLoading.value = false
  }
}

// 执行支付宝HTML内容
function executeAlipayHtml(htmlContent: string) {
  try {
    // 解析HTML内容，提取表单信息
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlContent, 'text/html')
    const form = doc.querySelector('form')

    if (form) {
      // 获取表单的action和所有input
      const action = form.getAttribute('action')
      const inputs = form.querySelectorAll('input[type="hidden"]')

      if (action) {
        // 创建一个新的表单并在新窗口中提交
        const newForm = document.createElement('form')
        newForm.method = 'post'
        newForm.action = action
        newForm.target = '_blank' // 在新窗口中打开
        newForm.style.display = 'none'

        // 复制所有隐藏输入
        inputs.forEach((input) => {
          const newInput = document.createElement('input')
          newInput.type = 'hidden'
          newInput.name = input.getAttribute('name') || ''
          newInput.value = input.getAttribute('value') || ''
          newForm.appendChild(newInput)
        })

        // 添加到页面并提交
        document.body.appendChild(newForm)
        newForm.submit()

        // 清理表单
        setTimeout(() => {
          try {
            if (newForm && newForm.parentNode) {
              document.body.removeChild(newForm)
            }
          }
          catch (cleanupError) {
            console.warn('清理表单时出错:', cleanupError)
          }
        }, 1000)
      }
    }
  }
  catch (error) {
    console.error('执行支付宝HTML时出错:', error)
    // 如果解析失败，回退到原来的iframe方式
    executeAlipayHtmlFallback(htmlContent)
  }
}

// 备用的iframe方式（保留作为后备方案）
function executeAlipayHtmlFallback(htmlContent: string) {
  try {
    const iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    iframe.style.position = 'absolute'
    iframe.style.left = '-9999px'
    iframe.style.top = '-9999px'
    iframe.style.width = '1px'
    iframe.style.height = '1px'

    document.body.appendChild(iframe)

    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
    if (iframeDoc) {
      iframeDoc.open()
      iframeDoc.write(htmlContent)
      iframeDoc.close()
    }

    setTimeout(() => {
      try {
        if (iframe && iframe.parentNode) {
          document.body.removeChild(iframe)
        }
      }
      catch (cleanupError) {
        console.warn('清理iframe时出错:', cleanupError)
      }
    }, 2000)
  }
  catch (error) {
    console.error('备用方式执行支付宝HTML时出错:', error)
  }
}

// 关闭支付弹窗
function closePaymentDialog() {
  showPaymentDialog.value = false
  qrCodeUrl.value = ''
  paymentHtml.value = ''
}

// 处理支付宝跳转
function handleAlipayPayment() {
  // 防止重复点击
  if (paymentLoading.value) {
    return
  }

  if (paymentHtml.value) {
    // 执行支付宝HTML跳转
    executeAlipayHtml(paymentHtml.value)
    // 关闭弹窗并开始轮询（不显示轮询弹窗）
    closePaymentDialog()
    startPaymentPolling(false)
  }
}

// 开始轮询支付状态
function startPaymentPolling(showMessage = false) {
  if (!currentOrderId.value) {
    console.error('订单ID不存在，无法开始轮询')
    return
  }

  // 重置支付状态
  paymentStatus.value = ''
  isPaymentCompleted.value = false

  const pollingStartTime = Date.now()

  // 立即查询一次
  queryPaymentStatus(showMessage)

  // 开始定时轮询（每3秒查询一次）
  pollingTimer.value = window.setInterval(() => {
    const elapsed = Date.now() - pollingStartTime

    // 检查是否超过最大轮询时间（2分钟）
    if (elapsed >= maxPollingTime) {
      stopPaymentPolling()
      if (showMessage) {
        ElMessage.warning('支付超时，请检查支付状态或重新支付')
      }
      return
    }

    // 继续查询支付状态
    queryPaymentStatus(showMessage)
  }, 3000)
}

// 查询支付状态
async function queryPaymentStatus(showDialog = true) {
  try {
    const response = await subscriptionsApi.queryOrderDetail(currentOrderId.value)

    if (response && typeof response.state !== 'undefined') {
      paymentStatus.value = response.state.toString()

      // 根据订单状态处理
      // 支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭
      if (response.state === 2) {
        // 支付成功
        isPaymentCompleted.value = true
        stopPaymentPolling()

        if (showDialog) {
          ElMessage.success('支付成功！')
        }

        // 可以跳转到成功页面或刷新数据
        console.log('支付成功，订单详情:', response)
      }
      else if (response.state === 3 || response.state === 4 || response.state === 6) {
        // 支付失败、已撤销、订单关闭
        stopPaymentPolling()

        if (showDialog) {
          const statusText = response.state === 3
            ? '支付失败'
            : response.state === 4 ? '订单已撤销' : '订单已关闭'
          ElMessage.error(`${statusText}，请重新支付`)
        }
      }
      // 其他状态（0-订单生成, 1-支付中, 5-已退款）继续轮询
    }
  }
  catch (error) {
    console.error('查询支付状态失败:', error)
    // 不停止轮询，继续尝试
  }
}

// 停止轮询
function stopPaymentPolling() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
}

// 手动检查支付状态
async function checkPaymentStatus() {
  // 防止重复点击
  if (paymentLoading.value) {
    return
  }

  try {
    paymentLoading.value = true

    if (currentOrderId.value) {
      // 手动查询时显示消息提示
      await queryPaymentStatus(true)
    }
    else {
      ElMessage.warning('订单信息不存在')
    }
  }
  catch (error) {
    console.error('检查支付状态失败:', error)
    ElMessage.error('检查支付状态失败')
  }
  finally {
    paymentLoading.value = false
  }
}

// 重置订单状态（用于重新创建订单）
function resetOrderStatus() {
  existingOrderId.value = ''
  isOrderCreated.value = false
  paymentStatus.value = ''
  isPaymentCompleted.value = false
  console.log('订单状态已重置')
}

// 支付回调
async function _startPayment() {

}

function _goToDashboard() {
  // 返回控制台
}

function _viewOrderDetail() {
  // 查看订单详情
}

function _handleRemove(_file: any, _fileList: any) {
  // 文件移除处理
}

function _handlePreview(_file: any) {
  // 文件预览处理
}

function _handleExceed(_files: any, _fileList: any) {
  // 文件超出限制处理
}

function _beforeRemove() {
  return true // 允许移除文件
}

// 清理函数
function cleanup() {
  // 清理支付相关状态
  showPaymentDialog.value = false
  paymentLoading.value = false
  qrCodeUrl.value = ''
  paymentHtml.value = ''

  // 清理轮询相关状态
  stopPaymentPolling()
  currentOrderId.value = ''

  // 清理支付状态
  paymentStatus.value = ''
  isPaymentCompleted.value = false

  // 清理订单状态（注意：这里可以选择是否清理订单状态）
  // 如果希望页面刷新后重新创建订单，可以取消注释下面两行
  // existingOrderId.value = ''
  // isOrderCreated.value = false

  // 清理可能残留的iframe
  const iframes = document.querySelectorAll('iframe[style*="display: none"]')
  iframes.forEach((iframe) => {
    try {
      if (iframe.parentNode) {
        iframe.parentNode.removeChild(iframe)
      }
    }
    catch (error) {
      console.warn('清理残留iframe时出错:', error)
    }
  })
}

onMounted(() => {
  // 初始化数据
})

onUnmounted(() => {
  // 组件卸载时清理资源
  cleanup()
})

// 路由守卫 - 离开页面前清理资源
onBeforeRouteLeave((_to, _from, next) => {
  // 清理支付相关资源
  cleanup()
  next()
})

// 解析metadata函数
function parseMetadata(metadata: string) {
  try {
    return JSON.parse(metadata)
  }
  catch {
    return null
  }
}

// 初始化数据
onMounted(() => {
  // 从路由参数获取套餐数据
  if (route.query.planData) {
    try {
      const planData = JSON.parse(route.query.planData as string)
      selectedPlan.value = {
        id: planData.packageCode,
        name: planData.packageName,
        description: planData.description,
        price: planData.price,
        userPrice: planData.userPrice,
        maxUsers: planData.maxUsers,
        features: planData.features || [],
      }
    }
    catch (error) {
      console.error('解析套餐数据失败:', error)
    }
  }

  // 从路由参数获取功能特性数据
  if (route.query.featuresData) {
    try {
      featuresData.value = JSON.parse(route.query.featuresData as string)
    }
    catch (error) {
      console.error('解析功能特性数据失败:', error)
    }
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto flex px-6 py-8 container">
      <!-- 主内容区 -->
      <div class="flex-1">
        <div class="rounded-lg bg-white p-6 shadow-sm">
          <div class="flex">
            <!-- 主表单区 -->
            <div class="flex-1 pr-6">
              <!-- 确认订购内容 -->
              <div>
                <div class="mb-8">
                  <h3 class="mb-4 text-lg text-gray-800 font-medium">
                    套餐详情
                  </h3>
                  <div class="rounded-lg bg-gray-50 p-6">
                    <div class="mb-4 flex items-start justify-between">
                      <div>
                        <h4 class="text-xl text-gray-800 font-semibold">
                          {{ selectedPlan.name }}
                        </h4>
                        <p class="mt-1 text-gray-600">
                          {{ selectedPlan.description }}
                        </p>
                        <p class="mt-1 text-sm text-gray-500">
                          最大用户数: {{ selectedPlan.maxUsers === -1 ? '无限制' : selectedPlan.maxUsers }}
                        </p>
                      </div>
                      <div class="text-2xl text-blue-600 font-bold">
                        ¥{{ selectedPlan.price }}
                      </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4">
                      <div class="mb-2 text-gray-700">
                        包含功能:
                      </div>
                      <div class="grid grid-cols-2 gap-2">
                        <div v-for="feature in selectedPlan.features" :key="feature" class="flex items-center">
                          <ElIcon class="mr-2 text-green-500">
                            <Check />
                          </ElIcon>
                          <span class="text-gray-600">{{ feature }}</span>
                        </div>
                      </div>

                      <!-- 显示功能特性详情 -->
                      <div v-if="featuresData.length > 0" class="mt-4">
                        <div class="mb-2 text-gray-700">
                          功能特性详情:
                        </div>
                        <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
                          <div v-for="feature in featuresData" :key="feature.id" class="border border-gray-200 rounded-lg bg-gray-50 p-3">
                            <div class="mb-2">
                              <div class="text-sm text-gray-800 font-medium">
                                {{ feature.featureName || '功能名称' }}
                              </div>
                            </div>
                            <div class="text-xs text-gray-600 space-y-1">
                              <div v-if="feature.usageType" class="flex items-center justify-between">
                                <span>使用类型:</span>
                                <el-tag size="small" :type="feature.usageType === 1 ? 'success' : 'info'">
                                  {{ feature.usageType === 1 ? '次数限制' : '其他类型' }}
                                </el-tag>
                              </div>
                              <div v-if="feature.quotaTotal" class="flex items-center justify-between">
                                <span>配额总数:</span>
                                <span class="font-medium">{{ feature.quotaTotal === -1 ? '无限制' : feature.quotaTotal.toLocaleString() }}</span>
                              </div>
                              <div v-if="feature.productPackageName" class="flex items-center justify-between">
                                <span>所属套餐:</span>
                                <span class="font-medium">{{ feature.productPackageName }}</span>
                              </div>
                              <div v-if="feature.metadata" class="mt-2">
                                <div class="text-xs text-gray-500">
                                  附加信息:
                                </div>
                                <div class="mt-1 text-xs text-gray-600">
                                  <template v-if="typeof feature.metadata === 'string'">
                                    <div v-if="parseMetadata(feature.metadata) && typeof parseMetadata(feature.metadata) === 'object'">
                                      <div v-for="(value, key) in parseMetadata(feature.metadata)" :key="key" class="flex items-center justify-between py-1">
                                        <span class="font-medium">{{ value }}</span>
                                      </div>
                                    </div>
                                  </template>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 订单摘要侧边栏 -->
            <div class="w-80">
              <div class="sticky top-6 rounded-lg bg-gray-50 p-6 shadow-sm">
                <h3 class="mb-4 text-lg text-gray-800 font-medium">
                  订单摘要
                </h3>

                <div class="mb-4 border-b border-gray-200 pb-4">
                  <div class="mb-2 flex items-center justify-between">
                    <div class="text-gray-600">
                      套餐
                    </div>
                    <div class="font-medium">
                      {{ selectedPlan.name }}
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="text-gray-600">
                      订购周期
                    </div>
                    <div class="font-medium">
                      {{ selectedBilling === 'month' ? '月付' : '年付' }}
                    </div>
                  </div>
                </div>

                <div class="mb-4 border-b border-gray-200 pb-4">
                  <div class="mb-2 text-gray-600">
                    费用明细
                  </div>
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <div class="text-gray-600">
                        基础费用
                      </div>
                      <div>¥{{ selectedPlan.price }}</div>
                    </div>
                    <div class="flex justify-between">
                      <div class="text-gray-600">
                        用户费用 ({{ userCount }}用户)
                      </div>
                      <div>¥{{ (selectedPlan.userPrice * userCount).toFixed(2) }}</div>
                    </div>
                    <div v-if="selectedServices.length > 0" class="pt-2">
                      <div class="mb-1 text-gray-600">
                        附加服务
                      </div>
                      <div v-for="service in selectedServices" :key="service.id" class="flex justify-between text-sm">
                        <div class="text-gray-600">
                          {{ service.name }}
                        </div>
                        <div>¥{{ service.price }}</div>
                      </div>
                    </div>
                    <div v-if="appliedCoupon" class="flex justify-between text-green-600">
                      <div>优惠折扣</div>
                      <div>-¥{{ appliedCoupon.discount }}</div>
                    </div>
                  </div>
                </div>

                <!-- 支付方式选择 -->
                <div class="mb-4 border-b border-gray-200 pb-4">
                  <div class="mb-3 text-gray-600">
                    支付方式
                  </div>
                  <ElRadioGroup v-model="paymentMethod" class="w-full">
                    <div class="space-y-2">
                      <ElRadio :value="1" class="w-full">
                        <div class="flex items-center">
                          <div class="mr-2 text-green-500">
                            💬
                          </div>
                          <span>微信支付</span>
                        </div>
                      </ElRadio>
                      <ElRadio :value="0" class="w-full">
                        <div class="flex items-center">
                          <div class="mr-2 text-blue-500">
                            💰
                          </div>
                          <span>支付宝</span>
                        </div>
                      </ElRadio>
                    </div>
                  </ElRadioGroup>
                </div>

                <div class="mb-4">
                  <div class="flex items-center justify-between">
                    <div class="text-gray-600">
                      总计
                    </div>
                    <div class="text-2xl text-blue-600 font-bold">
                      ¥{{ calculateTotalPrice() }}
                    </div>
                  </div>
                </div>

                <div class="space-y-3">
                  <ElButton
                    :type="isPaymentCompleted ? 'success' : 'primary'"
                    class="w-full"
                    :disabled="isPaymentCompleted || paymentLoading"
                    :loading="paymentLoading"
                    @click="confirmPayment"
                  >
                    {{ isPaymentCompleted ? '已支付' : (paymentLoading ? '处理中...' : '确认支付') }}
                  </ElButton>
                  <!-- <ElButton type="text" class="w-full" @click="_saveForLater">
                    保存并稍后继续
                  </ElButton> -->
                  <ElButton class="w-full text-gray-500 !ml-0" @click="_confirmCancel">
                    返回
                  </ElButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 支付弹窗 -->
    <ElDialog
      v-model="showPaymentDialog"
      :title="qrCodeUrl ? '微信扫码支付' : '支付宝支付'"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="text-center">
        <!-- 微信支付二维码 -->
        <div v-if="qrCodeUrl" class="mb-4">
          <div class="mb-2 text-gray-600">
            请使用微信扫描二维码完成支付
          </div>
          <div class="flex justify-center">
            <img
              :src="generateQRCode(qrCodeUrl)"
              alt="微信支付二维码"
              class="border border-gray-200 rounded-lg"
              style="width: 200px; height: 200px;"
            >
          </div>
          <div class="mt-2 text-sm text-gray-500">
            支付完成后页面将自动跳转
          </div>
        </div>

        <!-- 支付宝支付确认 -->
        <div v-if="paymentHtml && !qrCodeUrl" class="mb-4">
          <div class="mb-4">
            <div class="mb-2 text-6xl text-blue-500">
              💰
            </div>
            <div class="mb-2 text-gray-600">
              即将跳转到支付宝支付页面
            </div>
            <div class="text-sm text-gray-500">
              点击"前往支付宝"按钮完成支付
            </div>
          </div>
        </div>

        <div v-if="paymentLoading" class="py-8">
          <div class="text-gray-600">
            正在处理支付请求...
          </div>
        </div>
      </div>

      <template #footer>
        <div class="text-center">
          <ElButton @click="closePaymentDialog">
            取消支付
          </ElButton>
          <!-- 微信支付按钮 -->
          <ElButton
            v-if="qrCodeUrl"
            type="primary"
            :loading="paymentLoading"
            :disabled="paymentLoading"
            @click="checkPaymentStatus"
          >
            我已完成支付
          </ElButton>
          <!-- 支付宝支付按钮 -->
          <ElButton
            v-if="paymentHtml && !qrCodeUrl"
            type="primary"
            :loading="paymentLoading"
            :disabled="paymentLoading"
            @click="handleAlipayPayment"
          >
            前往支付宝
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
/* 隐藏数字输入框的箭头 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type='number'] {
  -moz-appearance: textfield;
}

/* 支付方式选择器样式 */
:deep(.el-radio-group .el-radio) {
  margin-right: 0;
  margin-bottom: 8px;
  width: 100%;
}

:deep(.el-radio-group .el-radio:last-child) {
  margin-bottom: 0;
}

:deep(.el-radio-group .el-radio__label) {
  padding-left: 8px;
}

/* 自定义上传组件样式 */
:deep(.el-upload-list__item) {
  transition: all 0.3s;
}
:deep(.el-upload-list__item:hover) {
  background-color: #f5f7fa;
}
</style>
