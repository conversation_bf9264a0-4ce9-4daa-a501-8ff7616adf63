<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowDown as ElIconArrowDown,
  ArrowRight as ElIconArrowRight,
  Bell as ElIconBell,
  ChatLineRound as ElIconChatLineRound,
  Check as ElIconCheck,
  Close as ElIconClose,
  Document as ElIconDocument,
  Phone as ElIconPhone,
  Search as ElIconSearch,
  User as ElIconUser,
  VideoPlay as ElIconVideoPlay,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import subscriptionsApi from '@/api/subscriptions'
import useUserStore from '@/store/modules/user'

const router = useRouter()

const _userStore = useUserStore()

// 套餐数据
const billingCycle = ref<'monthly' | 'yearly'>('monthly')
const selectedPlan = ref<any>(null)

// API数据状态
const packageList = ref<any[]>([])
const packageLoading = ref(false)
const currentPage = ref(0)
const _pageSize = ref(4)
const totalPages = ref(0)

// 选中的套餐功能特性
const selectedFeatureCode = ref<string>('')
const licenseFeatures = ref<any[]>([])
const featuresLoading = ref(false)

const plans = ref([
  {
    id: 'basic',
    name: '基础版',
    monthlyPrice: 399,
    yearlyPrice: 3599,
    features: [
      '最多 50 用户',
      '基础功能模块',
      '标准报表',
      '邮件支持',
      '5GB 存储空间',
    ],
    recommended: false,
    current: false,
  },
  {
    id: 'professional',
    name: '专业版',
    monthlyPrice: 0.01,
    yearlyPrice: 7999,
    features: [
      '最多 200 用户',
      '高级功能模块',
      '自定义报表',
      '电话支持',
      '50GB 存储空间',
      'API 访问',
    ],
    recommended: true,
    current: false,
  },
  {
    id: 'enterprise',
    name: '企业版',
    monthlyPrice: 1999,
    yearlyPrice: 17999,
    features: [
      '无用户限制',
      '全部功能模块',
      '高级分析',
      '24/7 专属支持',
      '500GB 存储空间',
      'API 高级访问',
      '单点登录',
    ],
    recommended: false,
    current: true,
  },
  {
    id: 'custom',
    name: '定制版',
    monthlyPrice: 0,
    yearlyPrice: 0,
    features: [
      '完全定制化',
      '专属客户经理',
      '私有化部署',
      '培训服务',
      '数据迁移',
      '定制开发',
    ],
    recommended: false,
    current: false,
  },
])

// 功能对比数据
const featureFilter = ref('all')
const featureSearch = ref('')
const featureCategories = ref([
  {
    name: '基础功能',
    expanded: false,
    features: [
      {
        name: '用户管理',
        description: '创建、编辑和管理用户账户',
        support: { basic: true, professional: true, enterprise: true, custom: true },
      },
      {
        name: '角色权限',
        description: '设置不同角色的访问权限',
        support: { basic: true, professional: true, enterprise: true, custom: true },
      },
      {
        name: '基础报表',
        description: '标准业务报表生成',
        support: { basic: true, professional: true, enterprise: true, custom: true },
      },
    ],
  },
  {
    name: '高级功能',
    expanded: false,
    features: [
      {
        name: '自定义工作流',
        description: '创建自定义业务流程',
        support: { basic: false, professional: true, enterprise: true, custom: true },
      },
      {
        name: '数据分析',
        description: '高级数据分析和可视化',
        support: { basic: false, professional: true, enterprise: true, custom: true },
      },
      {
        name: 'API 访问',
        description: '通过 API 集成其他系统',
        support: { basic: false, professional: true, enterprise: true, custom: true },
      },
    ],
  },
  {
    name: '企业级功能',
    expanded: false,
    features: [
      {
        name: '单点登录',
        description: '与企业身份提供商集成',
        support: { basic: false, professional: false, enterprise: true, custom: true },
      },
      {
        name: '审计日志',
        description: '完整系统操作记录',
        support: { basic: false, professional: false, enterprise: true, custom: true },
      },
      {
        name: '数据加密',
        description: '高级数据安全保护',
        support: { basic: false, professional: false, enterprise: true, custom: true },
      },
    ],
  },
])

// 定制配置数据
const userCount = ref(50)
const industries = ref(['互联网', '金融', '制造业', '零售', '教育', '医疗', '政府'])
const selectedIndustry = ref('')
const coreNeeds = ref(['数据分析', '移动端支持', '多语言支持', '高级安全', '定制开发'])
const selectedNeeds = ref<string[]>([])
const couponCode = ref('')

// 帮助与支持数据
const activeFAQ = ref<string[]>([])
const faqs = ref([
  {
    question: '如何选择合适的套餐？',
    answer: '根据企业规模、用户数量和功能需求选择。小型团队可从基础版开始，中型企业推荐专业版，大型组织建议企业版。',
  },
  {
    question: '套餐可以升级或降级吗？',
    answer: '可以随时升级套餐，升级后立即生效。降级需在当前计费周期结束后生效。',
  },
  {
    question: '是否有试用期？',
    answer: '我们提供 14 天免费试用，无需信用卡即可体验全部功能。',
  },
  {
    question: '如何计算用户数量？',
    answer: '用户数量指可以登录系统的员工账号数量，不包括仅查看报表的外部用户。',
  },
])

// 计算属性
const filteredFeatureCategories = computed(() => {
  return featureCategories.value.map((category) => {
    if (!featureSearch.value) { return category }

    const filteredFeatures = category.features.filter(feature =>
      feature.name.toLowerCase().includes(featureSearch.value.toLowerCase())
      || feature.description.toLowerCase().includes(featureSearch.value.toLowerCase()),
    )

    return {
      ...category,
      features: filteredFeatures,
    }
  })
})

// API调用函数
async function getPackageList(page = 0) {
  try {
    packageLoading.value = true
    const params = {
      pageNumber: page,
      pageSize: _pageSize.value,
    }
    const response = await subscriptionsApi.getPackageList(params)
    if (response) {
      packageList.value = response.content || []
      totalPages.value = response.totalPages || 0
    }
  }
  catch (error) {
    console.error('获取套餐列表失败:', error)
    ElMessage.error('获取套餐列表失败')
  }
  finally {
    packageLoading.value = false
  }
}

async function getLicenseFeatures(productPackageCode: string) {
  try {
    featuresLoading.value = true
    const params = {
      productPackageCode,
      page: 0,
      size: 10,
    }
    const response = await subscriptionsApi.getLicenseFeatures(params)
    if (response) {
      licenseFeatures.value = response.content || []
    }
  }
  catch (error) {
    console.error('获取功能特性失败:', error)
    ElMessage.error('获取功能特性失败')
  }
  finally {
    featuresLoading.value = false
  }
}

// 方法
function selectPlan(plan: any) {
  if (plan.current) { return }
  selectedPlan.value = plan
  selectedFeatureCode.value = plan.featureCode || ''

  // 如果有功能编码，获取功能特性
  if (plan.featureCode) {
    getLicenseFeatures(plan.featureCode)
  }
}

function selectPackage(packageItem: any) {
  // 设置选中状态
  packageList.value.forEach((item) => {
    item.selected = false
  })
  packageItem.selected = true

  // 获取功能特性 - 使用 packageCode 作为 featureCode
  if (packageItem.packageCode) {
    selectedFeatureCode.value = packageItem.packageCode
    getLicenseFeatures(packageItem.packageCode)
  }
}

// 解析 metadata 字段为对象
function parseMetadata(metadata: string) {
  try {
    return JSON.parse(metadata)
  }
  catch {
    return null
  }
}

// 格式化 metadata 字段
function formatMetadata(metadata: string) {
  try {
    const parsed = JSON.parse(metadata)
    const formatted = []

    if (parsed.trial_days) {
      formatted.push(`试用天数: ${parsed.trial_days}天`)
    }
    if (parsed.max_users) {
      if (parsed.max_users === -1) {
        formatted.push('最大用户数: 无限制')
      }
      else {
        formatted.push(`最大用户数: ${parsed.max_users}人`)
      }
    }

    // 处理其他可能的字段
    for (const [key, value] of Object.entries(parsed)) {
      if (key !== 'trial_days' && key !== 'max_users') {
        formatted.push(`${key}: ${value}`)
      }
    }

    return formatted.length > 0 ? formatted.join(', ') : metadata
  }
  catch {
    // 如果不是有效的JSON，直接返回原始字符串
    return metadata
  }
}

function showPlanDetails(plan: any) {
  // 实际项目中这里会跳转到详情页或打开模态框
  // 查看套餐详情
  ElMessage.info('查看套餐详情功能待实现')
}

function toggleCategory(category: any) {
  category.expanded = !category.expanded
}

function getCategorySupportLevel(plan: any, category: any) {
  const supportedFeatures = category.features.filter((f: any) => f.support[plan.id]).length
  const totalFeatures = category.features.length

  if (supportedFeatures === totalFeatures) { return 'full' }
  if (supportedFeatures > 0) { return 'partial' }
  return 'none'
}

function calculateUserFee() {
  if (!selectedPlan.value) { return 0 }

  const basePrice = billingCycle.value === 'monthly'
    ? selectedPlan.value.monthlyPrice
    : selectedPlan.value.yearlyPrice

  if (selectedPlan.value.id === 'custom') { return 0 }

  // 简化计算逻辑，实际项目会更复杂
  if (userCount.value <= 50) { return 0 }
  if (userCount.value <= 200) { return Math.round(basePrice * 0.2) }
  return Math.round(basePrice * 0.5)
}

function calculateDiscount() {
  if (billingCycle.value !== 'yearly' || !selectedPlan.value) { return 0 }
  return Math.round(selectedPlan.value.monthlyPrice * 12 - selectedPlan.value.yearlyPrice)
}

function calculateTotalPrice() {
  if (!selectedPlan.value) { return 0 }

  const basePrice = billingCycle.value === 'monthly'
    ? selectedPlan.value.monthlyPrice
    : selectedPlan.value.yearlyPrice

  const userFee = calculateUserFee()
  let total = basePrice + userFee

  // 应用优惠码折扣
  if (couponCode.value === 'SAVE10') {
    total = Math.round(total * 0.9)
  }

  return total
}

function proceedToCheckout() {
  // 检查是否选择了套餐
  const selectedPackage = packageList.value.find(item => item.selected)
  if (!selectedPackage) {
    ElMessage.warning('请先选择一个套餐')
    return
  }

  // 准备传递的套餐数据
  const planData = {
    packageCode: selectedPackage.packageCode,
    packageName: selectedPackage.packageName,
    description: selectedPackage.description || '企业级套餐解决方案',
    price: selectedPackage.price,
    userPrice: selectedPackage.userPrice || 0,
    maxUsers: JSON.parse(selectedPackage.metadata).max_users,
    features: selectedPackage.features || [],
    monthlyPrice: selectedPackage.price,
  }
  // 跳转到详情页面并传递数据
  router.push({
    name: '/systemSettings/subscriptionCenter/packagePlan/detail',
    query: {
      planData: JSON.stringify(planData),
      featuresData: JSON.stringify(licenseFeatures.value),
    },
  })
}

// 分页控制
function prevPage() {
  if (currentPage.value > 0) {
    currentPage.value -= 1
    getPackageList(currentPage.value)
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value - 1) {
    currentPage.value += 1
    getPackageList(currentPage.value)
  }
}

// 生命周期
onMounted(() => {
  // 默认选择推荐套餐
  const recommendedPlan = plans.value.find(p => p.recommended)
  if (recommendedPlan) { selectedPlan.value = recommendedPlan }

  // 获取套餐列表
  getPackageList()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl flex">
      <!-- 主内容区 -->
      <div class="flex-1 p-4">
        <h2 class="mb-6 text-2xl text-gray-800 font-bold">
          选择适合您企业的套餐
        </h2>
        <!-- 周期切换 -->
        <div v-if="false" class="mb-6 flex justify-end">
          <el-radio-group v-model="billingCycle" size="default">
            <el-radio-button label="monthly">
              按月订阅
            </el-radio-button>
            <el-radio-button label="yearly">
              按年订阅 (节省 20%)
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 套餐卡片 -->
        <div class="mb-10">
          <div v-loading="packageLoading" class="grid grid-cols-1 gap-6 lg:grid-cols-4 md:grid-cols-2">
            <div
              v-for="packageItem in packageList"
              :key="packageItem.id"
              class="cursor-pointer overflow-hidden border border-gray-200 rounded-lg bg-white shadow-sm transition-all duration-200 hover:shadow-md"
              :class="{ 'ring-2 ring-blue-500 border-blue-500': packageItem.selected }"
              @click="selectPackage(packageItem)"
            >
              <div class="p-6">
                <div class="mb-4 flex items-start justify-between">
                  <h3 class="text-lg text-gray-800 font-bold">
                    {{ packageItem.packageName || '套餐名称' }}
                  </h3>
                  <!-- <div class="flex items-center space-x-2">
                    <div v-if="packageItem.selected" class="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                      已选中
                    </div>
                     <div v-if="packageItem.upStatus" class="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
                      可用
                    </div>
                  </div> -->
                </div>

                <div class="mb-4">
                  <div class="text-sm text-gray-600">
                    <div v-if="packageItem.applicant" class="mb-2">
                      <span class="font-medium">适用对象:</span> {{ packageItem.applicant }}
                    </div>
                    <div v-if="packageItem.packageCode" class="mb-2">
                      <span class="font-medium">套餐编码:</span> {{ packageItem.packageCode }}
                    </div>
                    <div v-if="packageItem.packageType !== undefined" class="mb-2">
                      <span class="font-medium">套餐类型:</span>
                      <el-tag size="small" :type="packageItem.packageType === 0 ? 'info' : 'success'" class="ml-2">
                        {{ packageItem.packageType === 0 ? '试用版' : '正式版' }}
                      </el-tag>
                    </div>
                    <div v-if="packageItem.subscriptionMode !== undefined" class="mb-2">
                      <span class="font-medium">订阅模式:</span>
                      <el-tag size="small" :type="packageItem.subscriptionMode === 0 ? 'warning' : 'primary'" class="ml-2">
                        {{ packageItem.subscriptionMode === 0 ? '免费' : '付费' }}
                      </el-tag>
                    </div>
                    <div class="mb-2">
                      <span class="font-medium">价格:</span>
                      <span class="text-lg text-green-600 font-bold">
                        {{ packageItem.price === 0 ? ' 免费' : ` ¥${packageItem.price}` }}
                      </span>
                    </div>
                  </div>
                </div>

                <div v-if="packageItem.description" class="mb-4 text-sm text-gray-600">
                  <span class="font-medium">套餐描述:</span> {{ packageItem.description }}
                </div>

                <div v-if="packageItem.metadata" class="mb-4">
                  <div class="mb-1 text-xs text-gray-500">
                    <span class="font-medium">套餐详情:</span>
                  </div>
                  <div class="rounded bg-gray-50 p-2 text-xs text-gray-600">
                    {{ formatMetadata(packageItem.metadata) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="!packageLoading && packageList.length === 0" class="py-8 text-center text-gray-500">
            暂无套餐数据
          </div>
        </div>

        <!-- 功能特性详情 -->
        <div v-if="selectedFeatureCode && licenseFeatures.length > 0" class="mb-10 border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
          <div class="mb-6 flex items-center justify-between">
            <h3 class="text-lg text-gray-800 font-bold">
              功能特性详情
            </h3>
            <el-tag type="info" size="small">
              {{ selectedFeatureCode }}
            </el-tag>
          </div>

          <div class="grid grid-cols-1 gap-4 lg:grid-cols-3">
            <!-- 功能特性列表 - 占2份 -->
            <div v-loading="featuresLoading" class="lg:col-span-2">
              <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div
                  v-for="feature in licenseFeatures"
                  :key="feature.id"
                  class="border border-gray-200 rounded-lg bg-gray-50 p-4"
                >
                  <div class="mb-3">
                    <h4 class="text-md text-gray-800 font-bold">
                      {{ feature.featureName || '功能名称' }}
                    </h4>
                  </div>

                  <div class="text-sm text-gray-600 space-y-2">
                    <div v-if="feature.usageType" class="flex items-center justify-between">
                      <span>使用类型:</span>
                      <el-tag size="small" :type="feature.usageType === 1 ? 'success' : 'info'">
                        {{ feature.usageType === 1 ? '次数限制' : '其他类型' }}
                      </el-tag>
                    </div>
                    <div v-if="feature.quotaTotal" class="flex items-center justify-between">
                      <span>配额总数:</span>
                      <span class="font-medium">{{ feature.quotaTotal === -1 ? '无限制' : feature.quotaTotal.toLocaleString() }}</span>
                    </div>
                    <div v-if="feature.productPackageName" class="flex items-center justify-between">
                      <span>所属套餐:</span>
                      <span class="font-medium">{{ feature.productPackageName }}</span>
                    </div>
                    <div v-if="feature.metadata" class="mt-3">
                      <div class="text-xs text-gray-500">
                        附加信息:
                      </div>
                      <div class="mt-1 text-xs text-gray-600">
                        <template v-if="typeof feature.metadata === 'string'">
                          <div v-if="parseMetadata(feature.metadata) && typeof parseMetadata(feature.metadata) === 'object'">
                            <div v-for="(value, key) in parseMetadata(feature.metadata)" :key="key" class="flex items-center justify-between py-1">
                              <!-- <span class="text-gray-500">{{ key }}:</span> -->
                              <span class="font-medium">{{ value }}</span>
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 价格计算 - 占1份 -->
            <div class="lg:col-span-1">
              <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
                <h3 class="mb-6 text-lg text-gray-800 font-bold">
                  价格计算
                </h3>

                <div class="space-y-4">
                  <div class="flex justify-between">
                    <span class="text-gray-600">基础套餐费用</span>
                    <span class="font-medium">¥{{ selectedPlan ? (billingCycle === 'monthly' ? selectedPlan.monthlyPrice : selectedPlan.yearlyPrice) : 0 }}</span>
                  </div>

                  <div class="flex justify-between">
                    <span class="text-gray-600">用户数量费用 ({{ userCount }} 用户)</span>
                    <span class="font-medium">¥{{ calculateUserFee() }}</span>
                  </div>

                  <div v-if="billingCycle === 'yearly'" class="flex justify-between">
                    <span class="text-gray-600">年度折扣</span>
                    <span class="text-green-500">-¥{{ calculateDiscount() }}</span>
                  </div>

                  <!-- <div class="border-t border-gray-200 pt-4">
                    <div class="flex items-center justify-between">
                      <span class="text-gray-600">优惠码</span>
                      <el-input v-model="couponCode" placeholder="输入优惠码" size="small" class="w-32" />
                    </div>
                  </div> -->

                  <div class="border-t border-gray-200 pt-4">
                    <div class="flex items-center justify-between">
                      <span class="text-gray-600">总价</span>
                      <span class="text-2xl text-blue-600 font-bold">¥{{ calculateTotalPrice() }}</span>
                    </div>
                  </div>

                  <div class="pt-4">
                    <el-button
                      type="primary"
                      size="default"
                      class="w-full"
                      @click="proceedToCheckout"
                    >
                      继续购买
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="!featuresLoading && licenseFeatures.length === 0" class="py-8 text-center text-gray-500">
            暂无功能特性数据
          </div>
        </div>

        <!-- 功能对比区 -->
        <div v-if="false" class="mb-10 border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
          <div class="mb-6 flex items-center justify-between">
            <h3 class="text-lg text-gray-800 font-bold">
              功能对比
            </h3>
            <div class="flex space-x-2">
              <el-select v-model="featureFilter" placeholder="显示选项" size="small" class="w-32">
                <el-option label="全部功能" value="all" />
                <el-option label="仅差异" value="diff" />
              </el-select>
              <el-input
                v-model="featureSearch"
                placeholder="搜索功能..."
                size="small"
                class="w-48"
                clearable
              >
                <template #prefix>
                  <el-icon><ElIconSearch /></el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table class="w-full text-left text-sm">
              <thead class="bg-gray-50 text-xs text-gray-700 uppercase">
                <tr>
                  <th scope="col" class="w-1/3 px-6 py-3">
                    功能分类
                  </th>
                  <th v-for="plan in plans" :key="plan.id" scope="col" class="px-6 py-3">
                    {{ plan.name }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <template v-for="category in filteredFeatureCategories" :key="category.name">
                  <tr class="cursor-pointer border-b bg-white hover:bg-gray-50" @click="toggleCategory(category)">
                    <th scope="row" class="px-6 py-4 text-gray-900 font-medium">
                      <div class="flex items-center">
                        <el-icon class="mr-2">
                          <ElIconArrowRight v-if="!category.expanded" />
                          <ElIconArrowDown v-else />
                        </el-icon>
                        {{ category.name }}
                      </div>
                    </th>
                    <td v-for="plan in plans" :key="plan.id" class="px-6 py-4">
                      <div v-if="category.expanded" class="h-4" />
                      <template v-else>
                        <span v-if="getCategorySupportLevel(plan, category) === 'full'" class="text-green-500">完整支持</span>
                        <span v-else-if="getCategorySupportLevel(plan, category) === 'partial'" class="text-yellow-500">部分支持</span>
                        <span v-else class="text-gray-400">不支持</span>
                      </template>
                    </td>
                  </tr>

                  <template v-if="category.expanded">
                    <tr v-for="feature in category.features" :key="feature.name" class="border-b bg-white hover:bg-gray-50">
                      <td class="px-6 py-4 pl-12">
                        <div class="flex items-center">
                          {{ feature.name }}
                          <el-tooltip effect="light" placement="right">
                            <template #content>
                              <div class="max-w-xs p-2">
                                {{ feature.description }}
                              </div>
                            </template>
                          </el-tooltip>
                        </div>
                      </td>
                      <td v-for="plan in plans" :key="plan.id" class="px-6 py-4">
                        <el-icon v-if="(feature.support as any)[plan.id]" class="text-green-500">
                          <ElIconCheck />
                        </el-icon>
                        <el-icon v-else class="text-gray-300">
                          <ElIconClose />
                        </el-icon>
                      </td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 定制配置区 -->
        <div v-if="false" class="grid grid-cols-1 mb-10 gap-6 lg:grid-cols-3">
          <!-- 用户需求配置 -->
          <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm lg:col-span-2">
            <h3 class="mb-6 text-lg text-gray-800 font-bold">
              定制您的套餐
            </h3>

            <div class="space-y-6">
              <div>
                <label class="mb-2 block text-sm text-gray-700 font-medium">预计用户数量</label>
                <div class="flex items-center space-x-4">
                  <el-slider v-model="userCount" :min="1" :max="1000" :step="1" show-input />
                  <span class="whitespace-nowrap text-sm text-gray-500">{{ userCount }} 用户</span>
                </div>
              </div>

              <div>
                <label class="mb-2 block text-sm text-gray-700 font-medium">所属行业</label>
                <el-select v-model="selectedIndustry" placeholder="请选择行业" class="w-full">
                  <el-option
                    v-for="industry in industries"
                    :key="industry"
                    :label="industry"
                    :value="industry"
                  />
                </el-select>
              </div>

              <div>
                <label class="mb-2 block text-sm text-gray-700 font-medium">核心需求</label>
                <el-checkbox-group v-model="selectedNeeds" class="grid grid-cols-2 gap-3">
                  <el-checkbox v-for="need in coreNeeds" :key="need" :label="need" />
                </el-checkbox-group>
              </div>
            </div>
          </div>

          <!-- 价格计算器 -->
          <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
            <h3 class="mb-6 text-lg text-gray-800 font-bold">
              价格计算
            </h3>

            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-gray-600">基础套餐费用</span>
                <span class="font-medium">¥{{ selectedPlan ? (billingCycle === 'monthly' ? selectedPlan.monthlyPrice : selectedPlan.yearlyPrice) : 0 }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600">用户数量费用 ({{ userCount }} 用户)</span>
                <span class="font-medium">¥{{ calculateUserFee() }}</span>
              </div>

              <div v-if="billingCycle === 'yearly'" class="flex justify-between">
                <span class="text-gray-600">年度折扣</span>
                <span class="text-green-500">-¥{{ calculateDiscount() }}</span>
              </div>

              <div class="border-t border-gray-200 pt-4">
                <div class="flex items-center justify-between">
                  <!-- <span class="text-gray-600">优惠码</span> -->
                  <el-input v-model="couponCode" placeholder="输入优惠码" size="small" class="w-32" />
                </div>
              </div>

              <div class="border-t border-gray-200 pt-4">
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">总价</span>
                  <span class="text-2xl text-blue-600 font-bold">¥{{ calculateTotalPrice() }}</span>
                </div>
              </div>

              <div class="pt-4">
                <el-button
                  type="primary"
                  size="default"
                  class="w-full"
                  @click="proceedToCheckout"
                >
                  继续购买
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 帮助与支持区 -->
        <div v-if="false" class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
          <h3 class="mb-6 text-lg text-gray-800 font-bold">
            帮助与支持
          </h3>

          <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
            <!-- 常见问题 -->
            <div>
              <h4 class="text-md mb-4 text-gray-800 font-bold">
                常见问题
              </h4>
              <el-collapse v-model="activeFAQ">
                <el-collapse-item v-for="faq in faqs" :key="faq.question" :name="faq.question">
                  <template #title>
                    <span class="font-medium">{{ faq.question }}</span>
                  </template>
                  <div class="text-sm text-gray-600">
                    {{ faq.answer }}
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>

            <!-- 联系支持 -->
            <div>
              <h4 class="text-md mb-4 text-gray-800 font-bold">
                联系支持
              </h4>
              <div class="space-y-4">
                <div class="flex items-center">
                  <el-icon class="mr-3 text-blue-500">
                    <ElIconPhone />
                  </el-icon>
                  <div>
                    <div class="text-sm text-gray-800 font-medium">
                      销售咨询
                    </div>
                    <div class="text-xs text-gray-500">
                      ************
                    </div>
                  </div>
                </div>
                <div class="flex items-center">
                  <el-icon class="mr-3 text-blue-500">
                    <ElIconChatLineRound />
                  </el-icon>
                  <div>
                    <div class="text-sm text-gray-800 font-medium">
                      在线客服
                    </div>
                    <div class="text-xs text-gray-500">
                      周一至周五 9:00-18:00
                    </div>
                  </div>
                </div>
                <el-button
                  type="primary"
                  size="default"
                  class="w-full"
                >
                  预约演示
                </el-button>
              </div>
            </div>

            <!-- 资源下载 -->
            <div>
              <h4 class="text-md mb-4 text-gray-800 font-bold">
                资源下载
              </h4>
              <div class="space-y-3">
                <a href="#" class="flex items-center text-sm text-blue-600 hover:text-blue-700">
                  <el-icon class="mr-2"><ElIconDocument /></el-icon>
                  产品手册下载
                </a>
                <a href="#" class="flex items-center text-sm text-blue-600 hover:text-blue-700">
                  <el-icon class="mr-2"><ElIconVideoPlay /></el-icon>
                  功能介绍视频
                </a>
                <a href="#" class="flex items-center text-sm text-blue-600 hover:text-blue-700">
                  <el-icon class="mr-2"><ElIconUser /></el-icon>
                  客户见证集合
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.el-slider {
  flex: 1;
}

.el-collapse-item__header {
  padding: 8px 0;
}

.el-collapse-item__content {
  padding: 8px 0 16px;
}

/* 表格样式 */
table {
  border-collapse: collapse;
}

th, td {
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
}

/* 卡片悬停效果 */
.hover-shadow {
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
